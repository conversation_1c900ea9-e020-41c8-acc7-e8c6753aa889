"use server";

import { prisma } from "@/lib/prisma";
import type { MangaSeries } from "@/generated/prisma";

/**
 * Search for manga series by query string
 * @param query The search query
 * @returns Array of manga series matching the query
 */
export async function searchManga(query: string) {
  if (!query || query.trim() === "") {
    return [];
  }

  const searchTerm = query.trim();

  // Search for manga series matching the query in title, author, or genres
  const results = await prisma.mangaSeries.findMany({
    where: {
      OR: [
        { title: { contains: searchTerm, mode: "insensitive" } },
        { author: { contains: searchTerm, mode: "insensitive" } },
        { genres: { contains: searchTerm, mode: "insensitive" } },
        { description: { contains: searchTerm, mode: "insensitive" } },
      ],
    },
    select: {
      id: true,
      title: true,
      coverImage: true,
      author: true,
      status: true,
    },
    orderBy: {
      title: "asc",
    },
  });

  return results;
}
