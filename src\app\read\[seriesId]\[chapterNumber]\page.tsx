// src/app/read/[seriesId]/[chapterNumber]/page.tsx
import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { prisma } from '@/lib/prisma';
import { Button } from '@/components/ui/button';
import { Chapter } from '@/generated/prisma';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import ReaderControls from './ReaderControls'; // Added .tsx extension

interface ReaderPageProps {
	params: Promise<{
		seriesId: string;
		chapterNumber: string;
	}>;
}

async function getChapterData(seriesId: string, chapterNumber: string) {
	const chapter = await prisma.chapter.findFirst({
		where: {
			mangaSeriesId: seriesId,
			chapterNumber: chapterNumber,
		},
		include: {
			mangaSeries: {
				select: {
					id: true,
					title: true,
					chapters: { // Fetch all chapters for navigation
						select: { chapterNumber: true, id: true },
						orderBy: {
							// Attempt numeric sorting first, then fallback
							// This is a basic attempt; more robust sorting might be needed
							chapterNumber: 'asc',
						},
					},
				},
			},
			pages: {
				orderBy: {
					pageNumber: 'asc',
				},
				select: {
					id: true,
					pageNumber: true,
					imagePath: true,
				},
			},
		},
	});

	if (!chapter) {
		return null;
	}

	// Find previous and next chapters
	const allChapters = chapter.mangaSeries.chapters;
	const currentChapterIndex = allChapters.findIndex(
		(c: Pick<Chapter, 'chapterNumber' | 'id'>) => c.chapterNumber === chapterNumber // Added type for 'c'
	);
	const prevChapter =
		currentChapterIndex > 0 ? allChapters[currentChapterIndex - 1] : null;
	const nextChapter =
		currentChapterIndex < allChapters.length - 1
			? allChapters[currentChapterIndex + 1]
			: null;

	return {
		...chapter,
		prevChapter,
		nextChapter,
	};
}

export default async function ReaderPage({ params }: ReaderPageProps) {
	const { seriesId, chapterNumber } = await params;
	const chapterData = await getChapterData(seriesId, chapterNumber);

	if (!chapterData) {
		notFound();
	}

	const {
		title: chapterTitle,
		pages,
		mangaSeries,
		prevChapter,
		nextChapter,
	} = chapterData;
	const seriesTitle = mangaSeries.title;
	const allChapters = mangaSeries.chapters; // Already fetched

	return (
		<div className="container mx-auto px-4 py-8">
			<div className="mb-6 text-center">
				<Link href={`/series/${seriesId}`}>
					<h1 className="text-3xl font-bold hover:text-primary">
						{seriesTitle}
					</h1>
				</Link>
				<h2 className="text-xl text-muted-foreground">
					Chapter {chapterNumber}
					{chapterTitle ? `: ${chapterTitle}` : ''}
				</h2>
			</div>

			{/* Reader Controls Component */}
			<ReaderControls
				seriesId={seriesId}
				currentChapterNumber={chapterNumber}
				allChapters={allChapters}
				pages={pages}
				prevChapter={prevChapter}
				nextChapter={nextChapter}
			/>

			{/* Placeholder for reader content - will be handled by ReaderControls */}
			<div id="reader-content" className="mt-4">
				{/* Content will be rendered by the client component */}
			</div>
		</div>
	);
}

// Optional: Add metadata generation
export async function generateMetadata({ params }: ReaderPageProps) {
	const { seriesId, chapterNumber } = await params;
	const chapterData = await getChapterData(seriesId, chapterNumber);

	if (!chapterData) {
		return { title: 'Chapter Not Found' };
	}

	return {
		title: `${chapterData.mangaSeries.title} - Chapter ${chapterNumber}`,
		description: `Read Chapter ${chapterNumber} of ${chapterData.mangaSeries.title}`,
	};
}
