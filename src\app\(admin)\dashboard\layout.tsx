import Link from 'next/link';
import { ReactNode } from 'react';
import { But<PERSON> } from '@/components/ui/button'; // Assuming Button is correctly exported
import { signOut } from '@/auth'; // Assuming signOut is correctly exported from auth

export default function DashboardLayout({ children }: { children: ReactNode }) {
  return (
    <div className="flex min-h-screen bg-[#2a2a3c]">
      <aside className="w-64 bg-[#232334] p-6 border-r border-[#3d3d59]">
        <div className="flex items-center mb-8">
          <Link href="/" className="text-xl font-bold tracking-tight text-white">
            <span>TAROO</span>
            <span className="block text-xs text-gray-400 font-normal tracking-wider">ADMIN</span>
          </Link>
        </div>

        <h2 className="text-sm font-semibold mb-4 text-gray-400 uppercase tracking-wider">Menu</h2>
        <nav className="flex flex-col space-y-3">
          <Link
            href="/dashboard"
            className="text-gray-300 hover:text-primary transition-colors flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Dashboard
          </Link>
          <Link
            href="/dashboard/series"
            className="text-gray-300 hover:text-primary transition-colors flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
            Manga Series
          </Link>
          <Link
            href="/dashboard/chapters"
            className="text-gray-300 hover:text-primary transition-colors flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Chapters
          </Link>
        </nav>

        <div className="mt-auto pt-8">
           <form action={async () => {
             'use server';
             await signOut({ redirectTo: '/login' });
           }}>
             <Button type="submit" variant="outline" className="w-full bg-[#3d3d59] text-white hover:bg-[#4a4a69] border-none">
               Sign Out
             </Button>
           </form>
        </div>
      </aside>
      <main className="flex-1 p-6 text-white">{children}</main>
    </div>
  );
}
