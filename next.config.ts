/** @type {import('next').NextConfig} */
const nextConfig = {
    experimental: { // Add the experimental key
        serverActions: {
            bodySizeLimit: '10mb', // Increase limit for image uploads
        },
    },
    images: {
        formats: ['image/avif', 'image/webp'],
        deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048],
        imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
        remotePatterns: [
            {
                protocol: 'http',
                hostname: 'localhost',
                port: '3000',
                pathname: '/uploads/**',
            },
        ],
    },
    // Ensure static files are properly served
    async rewrites() {
        return [
            {
                source: '/uploads/:path*',
                destination: '/uploads/:path*',
            },
        ];
    },
};

export default nextConfig;
