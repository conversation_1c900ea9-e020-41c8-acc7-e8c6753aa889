import { prisma, DatabaseConnectionError } from '@/lib/prisma';
import { MangaCard } from '@/components/manga/MangaCard';
import Link from 'next/link';
import Image from 'next/image';
// Import the Prisma-generated type directly
import type { MangaSeries as PrismaMangaSeries, Chapter } from '@/generated/prisma';
import { HomePageWrapper } from './home-page-wrapper';

// Define the specific type needed for the MangaCard using the Prisma type
type MangaCardData = Pick<PrismaMangaSeries, 'id' | 'title' | 'coverImage' | 'author' | 'status'>;

// Define a type for the chapter data including the related series title
interface ChapterWithSeriesTitle extends Chapter {
  mangaSeries: {
    id: string;
    title: string;
  };
}

async function HomePageContent() {
  // Fetch series data needed for MangaCard
  const allSeries: MangaCardData[] = await prisma.mangaSeries.findMany({
    select: {
      id: true,
      title: true,
      coverImage: true,
      author: true, // Add author
      status: true, // Add status
    },
    orderBy: {
      title: 'asc',
    },
  });

  // Fetch latest chapters
  const latestChapters: ChapterWithSeriesTitle[] = await prisma.chapter.findMany({
    take: 15,
    orderBy: {
      uploadDate: 'desc',
    },
    include: {
      mangaSeries: {
        select: {
          id: true,
          title: true,
        },
      },
    },
  });

  try {
    return (
      <div className="space-y-12">
        {/* Hero Section */}
        <section className="bg-[#343450] rounded-lg overflow-hidden p-8 mb-8">
          <div className="max-w-xl">
            <h1 className="text-5xl font-bold text-white mb-2">Discover<br />Manga</h1>
            <p className="text-gray-300 mb-6">Explore our collection of manga</p>
            <Link
              href="/series"
              className="inline-block bg-[#8a7bff] hover:bg-[#7a6bff] text-white font-medium py-2 px-6 rounded-full transition-colors"
            >
              Browse
            </Link>
          </div>
        </section>

      {/* Latest Updates Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <section>
          <h2 className="text-2xl font-semibold mb-6 text-white">Latest Updates</h2>
          <div className="grid grid-cols-2 gap-4">
            {latestChapters.slice(0, 6).map((chapter: ChapterWithSeriesTitle) => {
              return (
                <Link
                  key={chapter.id}
                  href={`/read/${chapter.mangaSeries.id}/${chapter.chapterNumber}`}
                  className="block"
                >
                  <div className="bg-[#343450] rounded-lg overflow-hidden h-full">
                    <div className="p-4 text-white">
                      <h3 className="font-semibold text-base mb-1 line-clamp-1">
                        {chapter.mangaSeries.title}
                      </h3>
                      <p className="text-xs text-gray-300">
                        Chapter {chapter.chapterNumber}
                      </p>
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        </section>

        {/* Popular Manga Section */}
        <section>
          <h2 className="text-2xl font-semibold mb-6 text-white">Popular Manga</h2>
          <div className="grid grid-cols-2 gap-4">
            {allSeries.slice(0, 6).map((series: MangaCardData) => (
              <MangaCard key={series.id} manga={series} />
            ))}
          </div>
        </section>
      </div>

      {/* All Series Section */}
      <section className="mt-12">
        <h2 className="text-2xl font-semibold mb-6 text-white">All Series</h2>
        {allSeries.length > 0 ? (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
            {allSeries.map((series: MangaCardData) => (
              <MangaCard key={series.id} manga={series} />
            ))}
          </div>
        ) : (
          <p className="text-gray-300">No manga series found.</p>
        )}
      </section>
    </div>
  );
  } catch (error) {
    console.error('Error fetching data for homepage:', error);
    // Re-throw the error to be caught by the error boundary
    throw error instanceof Error
      ? error
      : new DatabaseConnectionError('Failed to load homepage data. Database connection issue.');
  }
}

export default async function HomePage() {
  return (
    <HomePageWrapper>
      <HomePageContent />
    </HomePageWrapper>
  );
}
