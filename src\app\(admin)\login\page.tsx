'use client';

import { useActionState } from 'react';
import { useFormStatus } from 'react-dom';
import { useRouter, useSearchParams } from 'next/navigation';
import { loginAction, type LoginFormState } from '@/actions/authActions'; // Import the server action and state type
import { useEffect, Suspense } from 'react';
import { toast } from 'sonner'; // Assuming sonner is used for notifications
import { useSession, signIn } from 'next-auth/react';

// Component to handle button pending state
function SubmitButton() {
  const { pending } = useFormStatus();
  return (
    <button
      type="submit"
      disabled={pending}
      className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary/50 disabled:opacity-50 transition-colors"
    >
      {pending ? 'Signing in...' : 'Sign in'}
    </button>
  );
}

// Client component that uses useSearchParams
function LoginForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/dashboard';
  const { data: session, status } = useSession();

  // Initial state for the form
  const initialState: LoginFormState = { message: null, errors: {}, success: false };
  const [state, dispatch] = useActionState(loginAction, initialState);

  // Handle form state changes
  useEffect(() => {
    if (state.message) {
      if (state.success) {
        // If login was successful, redirect manually
        toast.success('Login successful! Redirecting...');
        router.push(callbackUrl);
      } else {
        // Display specific field errors or general form error
        const errorMessage = state.errors?.credentials?.[0] || state.errors?._form?.[0] || state.message;

        // Show toast notification for error
        toast.error(errorMessage, {
          id: 'login-error', // Use an ID to prevent duplicate toasts
          duration: 4000,    // Show for 4 seconds
        });

        // Focus on the username field for better UX
        const usernameInput = document.getElementById('username');
        if (usernameInput) {
          usernameInput.focus();
        }
      }
    }
  }, [state, router, callbackUrl]);


  // Show loading state while checking session
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-pulse text-primary">Loading...</div>
      </div>
    );
  }

  // Only show the login form if not authenticated
  if (status === 'unauthenticated') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="max-w-md w-full space-y-8 p-8 bg-[#343450] rounded-lg shadow-lg">
          <div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-white">
              Admin Login
            </h2>
          </div>

          {/* Use the server action */}
          <form className="mt-8 space-y-6" action={dispatch}>
            {/* Display error message box if there are any errors */}
            {(state.errors?._form || state.errors?.credentials) && (
              <div className="bg-[#3d3d59]/50 border-l-4 border-red-500 p-4 mb-4 rounded animate-fadeIn">
                {state.errors?._form && (
                  <div>
                    {state.errors._form.map((error, index) => (
                      <p key={index} className="text-red-400">{error}</p>
                    ))}
                  </div>
                )}
                {state.errors?.credentials && (
                  <div>
                    {state.errors.credentials.map((error, index) => (
                      <p key={index} className="text-red-400">{error}</p>
                    ))}
                  </div>
                )}
              </div>
            )}

            <input type="hidden" name="callbackUrl" value={callbackUrl} />

            <div className="space-y-4">
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-300 mb-1">
                  Username
                </label>
                <input
                  id="username"
                  name="username"
                  type="text"
                  required
                  className="appearance-none relative block w-full px-3 py-2 border border-[#4a4a69] bg-[#2a2a3c] placeholder-gray-400 text-white rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm"
                  placeholder="Enter your username"
                />
              </div>
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-1">
                  Password
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  required
                  className="appearance-none relative block w-full px-3 py-2 border border-[#4a4a69] bg-[#2a2a3c] placeholder-gray-400 text-white rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm"
                  placeholder="Enter your password"
                />
              </div>
            </div>

            <div>
              {/* Use the SubmitButton component */}
              <SubmitButton />
            </div>
          </form>

          {/* Divider */}
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-[#4a4a69]" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-[#2a2a3c] text-gray-400">Or continue with</span>
              </div>
            </div>
          </div>

          {/* Discord Sign-in Button */}
          <div className="mt-6">
            <button
              type="button"
              onClick={() => signIn('discord', { callbackUrl })}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-[#5865F2] hover:bg-[#4752C4] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#5865F2] transition-colors duration-200"
            >
              <span className="absolute left-0 inset-y-0 flex items-center pl-3">
                <svg className="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515a.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0a12.64 12.64 0 0 0-.617-1.25a.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057a19.9 19.9 0 0 0 5.993 3.03a.078.078 0 0 0 .084-.028a14.09 14.09 0 0 0 1.226-1.994a.076.076 0 0 0-.041-.106a13.107 13.107 0 0 1-1.872-.892a.077.077 0 0 1-.008-.128a10.2 10.2 0 0 0 .372-.292a.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127a12.299 12.299 0 0 1-1.873.892a.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028a19.839 19.839 0 0 0 6.002-3.03a.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.956-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.955-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.946 2.418-2.157 2.418z"/>
                </svg>
              </span>
              Sign in with Discord
            </button>
          </div>
        </div>
      </div>
    );
  }

  // If we get here, the user is authenticated but the redirect hasn't happened yet
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-primary">Redirecting to dashboard...</div>
    </div>
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-pulse text-primary">Loading...</div>
      </div>
    }>
      <LoginForm />
    </Suspense>
  );
}
