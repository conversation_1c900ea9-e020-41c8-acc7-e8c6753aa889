'use server';

import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';

export type SiteStatistics = {
  totalSeries: number;
  totalChapters: number;
  totalPages: number;
  seriesWithMostChapters: {
    id: string;
    title: string;
    chapterCount: number;
  } | null;
  latestSeries: {
    id: string;
    title: string;
    createdAt: Date;
  } | null;
  latestChapter: {
    id: string;
    chapterNumber: string;
    mangaSeriesId: string;
    mangaSeriesTitle: string;
    uploadDate: Date;
  } | null;
  seriesByStatus: {
    status: string;
    count: number;
  }[];
  chaptersByMonth: {
    month: string;
    count: number;
  }[];
};

/**
 * Fetch statistics for the dashboard stats page
 */
export async function getSiteStatistics(): Promise<SiteStatistics | null> {
  const session = await auth();
  if (!session?.user) {
    return null;
  }

  try {
    // Get total counts
    const totalSeries = await prisma.mangaSeries.count();
    const totalChapters = await prisma.chapter.count();
    const totalPages = await prisma.page.count();

    // Get series with most chapters
    const seriesWithChapterCounts = await prisma.mangaSeries.findMany({
      select: {
        id: true,
        title: true,
        _count: {
          select: {
            chapters: true,
          },
        },
      },
      orderBy: {
        chapters: {
          _count: 'desc',
        },
      },
      take: 1,
    });

    const seriesWithMostChapters = seriesWithChapterCounts.length > 0
      ? {
          id: seriesWithChapterCounts[0].id,
          title: seriesWithChapterCounts[0].title,
          chapterCount: seriesWithChapterCounts[0]._count.chapters,
        }
      : null;

    // Get latest series
    const latestSeriesData = await prisma.mangaSeries.findFirst({
      select: {
        id: true,
        title: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Get latest chapter
    const latestChapterData = await prisma.chapter.findFirst({
      select: {
        id: true,
        chapterNumber: true,
        uploadDate: true,
        mangaSeriesId: true,
        mangaSeries: {
          select: {
            title: true,
          },
        },
      },
      orderBy: {
        uploadDate: 'desc',
      },
    });

    const latestChapter = latestChapterData
      ? {
          id: latestChapterData.id,
          chapterNumber: latestChapterData.chapterNumber,
          mangaSeriesId: latestChapterData.mangaSeriesId,
          mangaSeriesTitle: latestChapterData.mangaSeries.title,
          uploadDate: latestChapterData.uploadDate,
        }
      : null;

    // Get series by status
    const seriesByStatusData = await prisma.mangaSeries.groupBy({
      by: ['status'],
      _count: {
        id: true,
      },
    });

    const seriesByStatus = seriesByStatusData.map(item => ({
      status: item.status || 'Unknown',
      count: item._count.id,
    }));

    // Get chapters by month (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const recentChapters = await prisma.chapter.findMany({
      where: {
        createdAt: {
          gte: sixMonthsAgo,
        },
      },
      select: {
        createdAt: true,
      },
    });

    // Group chapters by month
    const chaptersByMonthMap = new Map<string, number>();
    
    // Initialize the last 6 months
    for (let i = 0; i < 6; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      chaptersByMonthMap.set(monthKey, 0);
    }

    // Count chapters by month
    recentChapters.forEach(chapter => {
      const date = new Date(chapter.createdAt);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      
      if (chaptersByMonthMap.has(monthKey)) {
        chaptersByMonthMap.set(monthKey, chaptersByMonthMap.get(monthKey)! + 1);
      }
    });

    // Convert map to array and sort by month
    const chaptersByMonth = Array.from(chaptersByMonthMap.entries())
      .map(([month, count]) => ({ month, count }))
      .sort((a, b) => a.month.localeCompare(b.month));

    return {
      totalSeries,
      totalChapters,
      totalPages,
      seriesWithMostChapters,
      latestSeries: latestSeriesData,
      latestChapter,
      seriesByStatus,
      chaptersByMonth,
    };
  } catch (error) {
    console.error('Error fetching site statistics:', error);
    return null;
  }
}
