// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model AdminUser {
  id         String   @id @default(cuid())
  username   String?  @unique // Made optional for OAuth users
  password   String? // Made optional for OAuth users
  email      String?  @unique // For OAuth users
  name       String? // Display name
  image      String? // Profile image URL
  provider   String? // OAuth provider (e.g., "discord", "credentials")
  providerId String? // Provider-specific user ID
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([provider, providerId]) // Ensure unique provider/ID combination
}

model MangaSeries {
  id          String    @id @default(cuid())
  title       String
  description String?   @db.Text // Use Text for longer descriptions
  coverImage  String? // Path to the cover image file
  status      String? // e.g., "Ongoing", "Completed", "Hiatus"
  author      String?
  artist      String?
  genres      String? // Simple comma-separated string for MVP
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  chapters    Chapter[] // Relation to chapters
}

model Chapter {
  id            String      @id @default(cuid())
  chapterNumber String // Using String for flexibility (e.g., "10.5", "Extra")
  title         String?
  uploadDate    DateTime    @default(now())
  mangaSeriesId String
  mangaSeries   MangaSeries @relation(fields: [mangaSeriesId], references: [id], onDelete: Cascade) // Link back to series
  pages         Page[] // Relation to pages
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  @@index([mangaSeriesId])
  @@index([uploadDate]) // Index for sorting latest releases
}

model Page {
  id         String   @id @default(cuid())
  pageNumber Int // Sequence number (1, 2, 3...)
  imagePath  String // Path to the image file
  chapterId  String
  chapter    Chapter  @relation(fields: [chapterId], references: [id], onDelete: Cascade) // Link back to chapter
  createdAt  DateTime @default(now())

  @@index([chapterId])
  @@index([chapterId, pageNumber]) // For ordering pages within a chapter
}
