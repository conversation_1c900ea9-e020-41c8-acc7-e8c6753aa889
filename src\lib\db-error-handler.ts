import { toast } from 'sonner';

/**
 * Utility function to handle database errors consistently across the application
 * @param error The error object
 * @param customMessage Optional custom message to display
 */
export function handleDatabaseError(error: unknown, customMessage?: string): never {
  // Log the error to the console with detailed information
  console.error('Database Error:', error);
  
  // Determine if this is a connection error
  const isConnectionError = 
    error instanceof Error && 
    (error.message.includes("Can't reach database server") || 
     error.message.includes("connection") ||
     error.message.includes("ECONNREFUSED"));
  
  // Create a user-friendly error message
  const userMessage = customMessage || (
    isConnectionError
      ? "Cannot connect to the database. Please make sure your database server is running."
      : "A database error occurred. Please try again later."
  );
  
  // If we're on the client side, show a toast notification
  if (typeof window !== 'undefined') {
    toast.error('Database Error', {
      description: userMessage,
      duration: 5000,
    });
  }
  
  // Throw a new error with the user-friendly message
  throw new Error(userMessage);
}
