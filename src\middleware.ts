import NextAuth from 'next-auth';
import { authConfig } from './auth.config';

// Initialize NextAuth with the shared configuration
const { auth } = NextAuth(authConfig);

// Export the auth function directly.
// NextAuth.js automatically handles protecting routes based on the 'authorized' callback in auth.config.ts
// and the matcher configuration below.
export default auth;

// Optionally, don't invoke Middleware on some paths
// Read more: https://nextjs.org/docs/app/building-your-application/routing/middleware#matcher
export const config = {
  matcher: ['/dashboard/:path*', '/login'], // Protect dashboard, apply middleware logic to login page
};
