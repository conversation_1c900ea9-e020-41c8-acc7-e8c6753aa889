'use server';

import { signIn } from '@/auth';
import { AuthError } from 'next-auth';

export type LoginFormState = {
  message: string | null;
  errors?: {
    credentials?: string[];
    _form?: string[]; // General form errors
  };
  success: boolean;
};

export async function loginAction(
  _prevState: LoginFormState, // Prefix with underscore to indicate it's intentionally unused
  formData: FormData,
): Promise<LoginFormState> {
  const username = formData.get('username') as string;
  const password = formData.get('password') as string;

  if (!username || !password) {
    return {
      message: 'Username and password are required.',
      success: false,
      errors: { _form: ['Username and password are required.'] },
    };
  }

  try {
    // When using server actions with client-side redirect handling,
    // we need to set redirect: false to prevent NextAuth from trying to handle the redirect itself
    await signIn('credentials', {
      username,
      password,
      redirect: false, // Don't redirect automatically - we'll handle it in the client component
    });

    // If we reach here, the login was successful
    // Return success state so the client component can handle the redirect
    return { message: 'Login successful! Redirecting...', success: true };

  } catch (error) {
    // Handle authentication errors
    if (error instanceof AuthError) {
      switch (error.type) {
        case 'CredentialsSignin':
          // This is the most common error - invalid username or password
          // No need to log this error as it's expected behavior
          return {
            message: 'Invalid credentials.',
            success: false,
            errors: { credentials: ['Invalid username or password.'] }
          };
        case 'CallbackRouteError':
          // Only log in development environment
          if (process.env.NODE_ENV === 'development') {
            console.error("Callback Route Error:", error.cause?.err?.message);
          }
          return {
            message: 'Authentication callback failed.',
            success: false,
            errors: { _form: ['Login process failed. Please try again.'] }
          };
        default:
          // Only log in development environment
          if (process.env.NODE_ENV === 'development') {
            console.error("Authentication Error:", error);
          }
          return {
            message: 'Authentication failed.',
            success: false,
            errors: { _form: ['An error occurred during login. Please try again.'] }
          };
      }
    }

    // NextAuth v5 throws a NEXT_REDIRECT error for successful logins
    // This is expected behavior and not an actual error
    if (error instanceof Error && error.message.includes('NEXT_REDIRECT')) {
      // This is a successful login that's being redirected
      // We don't need to do anything here as Next.js will handle the redirect
      return { message: 'Login successful! Redirecting...', success: true };
    }
  }
}

export async function discordSignInAction(callbackUrl: string = '/dashboard') {
  try {
    await signIn('discord', { redirectTo: callbackUrl });
  } catch (error) {
    // Handle Discord sign-in errors
    if (error instanceof AuthError) {
      switch (error.type) {
        case 'OAuthSignInError':
          throw new Error('Discord sign-in failed. Please try again.');
        default:
          throw new Error('An error occurred during Discord sign-in.');
      }
    }
    throw error;

    // For any other unexpected errors
    // Only log in development environment
    if (process.env.NODE_ENV === 'development') {
      console.error("Unexpected Login Error:", error);
    }

    // Return a user-friendly error message
    return {
      message: 'Login failed.',
      success: false,
      errors: { _form: ['An error occurred during login. Please try again later.'] }
    };
  }
}
