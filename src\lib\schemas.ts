import { z } from 'zod';

// Zod schema for chapter creation form data
export const CreateChapterSchema = z.object({
  mangaSeriesId: z.string().cuid(),
  chapterNumber: z.string().min(1, 'Chapter number is required'),
  title: z.string().optional(),
  // We'll handle file validation within the action for now
  pages: z.any(), // Placeholder for file handling
});

// Add other schemas here as needed, e.g., for SeriesForm
