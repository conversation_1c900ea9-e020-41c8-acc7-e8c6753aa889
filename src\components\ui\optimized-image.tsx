'use client';

import Image from 'next/image';
import { cn } from '@/lib/utils';
import { getDefaultBlurDataURL, getImageSizes, getImageUrl } from '@/lib/imageUtils';

interface OptimizedImageProps {
  src: string | null;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean;
  priority?: boolean;
  className?: string;
  fallbackText?: string;
  fallbackClassName?: string;
  type?: 'cover' | 'page';
  sizesType?: 'card' | 'cover' | 'fullWidth' | 'thumbnail';
  usePlaceholder?: boolean;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  fill = false,
  priority = false,
  className,
  fallbackText = 'No Image',
  fallbackClassName,
  type = 'cover',
  sizesType = 'card',
  usePlaceholder = true,
  objectFit = 'cover',
}: OptimizedImageProps) {
  // Process the image URL
  const imageUrl = getImageUrl(src, type);
  
  // If no image URL is available, show a fallback
  if (!imageUrl) {
    return (
      <div className={cn(
        "bg-muted flex items-center justify-center", 
        fill ? "w-full h-full" : "",
        width ? `w-[${width}px]` : "",
        height ? `h-[${height}px]` : "",
        fallbackClassName
      )}>
        <span className="text-muted-foreground">{fallbackText}</span>
      </div>
    );
  }

  // Get the appropriate sizes attribute
  const sizes = getImageSizes(sizesType);

  // Determine the object-fit class
  const objectFitClass = `object-${objectFit}`;

  return (
    <Image
      src={imageUrl}
      alt={alt}
      width={!fill ? width : undefined}
      height={!fill ? height : undefined}
      fill={fill}
      priority={priority}
      className={cn(objectFitClass, className)}
      sizes={sizes}
      placeholder={usePlaceholder ? "blur" : undefined}
      blurDataURL={usePlaceholder ? getDefaultBlurDataURL() : undefined}
      loading={priority ? "eager" : "lazy"}
    />
  );
}
