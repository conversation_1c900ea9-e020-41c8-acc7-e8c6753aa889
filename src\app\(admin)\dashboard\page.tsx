import { auth } from '@/auth';

export default async function DashboardPage() {
  const session = await auth();

  return (
    <div>
      <h1 className="text-3xl font-bold mb-8 text-white">Dashboard</h1>

      <div className="bg-[#343450] rounded-lg shadow-lg p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4 text-white">
          Welcome, {session?.user?.name || 'Admin'}
        </h2>
        <p className="text-gray-300 mb-4">
          This is the admin dashboard for Taroo manga reader. Use the sidebar to navigate to different sections.
        </p>

        {/* Authentication Info */}
        <div className="mt-4 p-4 bg-[#2a2a3c] rounded-lg border border-[#4a4a69]">
          <h3 className="text-sm font-medium text-gray-400 mb-2">Account Information</h3>
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Authentication Method:</span>
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                session?.user?.provider === 'discord'
                  ? 'bg-[#5865F2] text-white'
                  : 'bg-[#4a4a69] text-gray-300'
              }`}>
                {session?.user?.provider === 'discord' ? 'Discord OAuth' : 'Username/Password'}
              </span>
            </div>

            {session?.user?.provider === 'discord' && session?.user?.discordUsername && (
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Discord Username:</span>
                <span className="text-white font-medium">{session.user.discordUsername}</span>
              </div>
            )}

            {session?.user?.originalUsername && (
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Admin Username:</span>
                <span className="text-white font-medium">{session.user.originalUsername}</span>
              </div>
            )}

            {session?.user?.email && (
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Email:</span>
                <span className="text-white font-medium">{session.user.email}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-[#343450] border-l-4 border-primary rounded-lg shadow-lg p-6 transition-transform hover:-translate-y-1 duration-200">
          <h3 className="text-lg font-semibold mb-2 text-white">Manga Series</h3>
          <p className="text-gray-300 mb-4">Manage your manga series collection</p>
          <a href="/dashboard/series" className="text-primary hover:text-primary/80 flex items-center">
            View Series
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </a>
        </div>

        <div className="bg-[#343450] border-l-4 border-[#6e6bff] rounded-lg shadow-lg p-6 transition-transform hover:-translate-y-1 duration-200">
          <h3 className="text-lg font-semibold mb-2 text-white">Chapters</h3>
          <p className="text-gray-300 mb-4">Manage chapters and uploads</p>
          <a href="/dashboard/chapters" className="text-[#6e6bff] hover:text-[#6e6bff]/80 flex items-center">
            View Chapters
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </a>
        </div>

        <div className="bg-[#343450] border-l-4 border-[#8a7bff] rounded-lg shadow-lg p-6 transition-transform hover:-translate-y-1 duration-200">
          <h3 className="text-lg font-semibold mb-2 text-white">Statistics</h3>
          <p className="text-gray-300 mb-4">View site statistics and analytics</p>
          <a href="/dashboard/stats" className="text-[#8a7bff] hover:text-[#8a7bff]/80 flex items-center">
            View Stats
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </a>
        </div>
      </div>
    </div>
  );
}
