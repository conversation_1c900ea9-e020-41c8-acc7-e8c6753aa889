// src/app/popular/page.tsx
import { prisma } from '@/lib/prisma';
import { MangaCard } from '@/components/manga/MangaCard';
import type { MangaSeries as PrismaMangaSeries } from '@/generated/prisma';

// Define the specific type needed for the MangaCard using the Prisma type
type MangaCardData = Pick<PrismaMangaSeries, 'id' | 'title' | 'coverImage' | 'author' | 'status'>;

export default async function PopularPage() {
  // In a real application, you would fetch popular manga based on views, ratings, etc.
  // For this MVP, we'll just fetch all series and assume they're popular
  const popularSeries: MangaCardData[] = await prisma.mangaSeries.findMany({
    select: {
      id: true,
      title: true,
      coverImage: true,
      author: true,
      status: true,
    },
    orderBy: {
      title: 'asc',
    },
  });

  return (
    <div className="space-y-8">
      <div className="bg-[#343450] rounded-lg p-8 mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Popular Manga</h1>
        <p className="text-gray-300">Discover the most popular manga series on Taroo</p>
      </div>

      {popularSeries.length > 0 ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
          {popularSeries.map((series: MangaCardData) => (
            <MangaCard key={series.id} manga={series} />
          ))}
        </div>
      ) : (
        <p className="text-gray-300">No manga series found.</p>
      )}
    </div>
  );
}
