'use client';

import { useActionState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { updateChapterDetails } from '@/actions/chapterActions';
import { useEffect } from 'react';
import { toast } from 'sonner';
import { Chapter, Page } from '@/generated/prisma'; // Using custom Prisma output path

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

// Schema for the form fields being edited
const FormSchema = z.object({
  chapterNumber: z.string().min(1, 'Chapter number is required'),
  title: z.string().optional(),
});

interface ChapterEditFormProps {
  chapter: Chapter; // Pass the initial chapter data
}

export function ChapterEditForm({ chapter }: ChapterEditFormProps) {
  const initialState = { message: null, errors: {} };
  // Bind chapterId to the action
  const updateActionWithId = updateChapterDetails.bind(null, chapter.id);
  const [state, formAction, isPending] = useActionState(updateActionWithId, initialState);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      chapterNumber: chapter.chapterNumber,
      title: chapter.title || '',
    },
  });

  useEffect(() => {
    if (state?.message) {
      if (state.errors && Object.keys(state.errors).length > 0) {
        toast.error("Update Failed", {
          description: state.message || "Please check the form for errors.",
        });
        // Set form errors from server action state
        if (state.errors.chapterNumber) {
          form.setError('chapterNumber', { message: state.errors.chapterNumber[0] });
        }
        if (state.errors.title) {
          form.setError('title', { message: state.errors.title[0] });
        }
      } else {
        toast.success("Update Successful", {
          description: state.message,
        });
        // Optionally reset form to new values if needed, though revalidation handles display
        // form.reset({ chapterNumber: chapter.chapterNumber, title: chapter.title || '' });
      }
    }
  }, [state, form, chapter]); // Add chapter to dependencies if resetting form

  return (
    <Form {...form}>
      <form action={formAction} className="space-y-6 max-w-lg">
        {/* Display general form errors from server */}
        {state?.errors?._form && (
          <div className="text-sm font-medium text-destructive">
            {state.errors._form.join(', ')}
          </div>
        )}

        <FormField
          control={form.control}
          name="chapterNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Chapter Number *</FormLabel>
              <FormControl>
                <Input placeholder="e.g., 1, 10.5, Extra" {...field} required />
              </FormControl>
              <FormMessage />
              {/* Display server-side errors for this field */}
              {state?.errors?.chapterNumber && !form.formState.errors.chapterNumber && (
                 <p className="text-sm font-medium text-destructive">{state.errors.chapterNumber.join(', ')}</p>
              )}
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title (Optional)</FormLabel>
              <FormControl>
                <Input placeholder="e.g., The Beginning" {...field} />
              </FormControl>
              <FormMessage />
               {/* Display server-side errors for this field */}
              {state?.errors?.title && !form.formState.errors.title && (
                 <p className="text-sm font-medium text-destructive">{state.errors.title.join(', ')}</p>
              )}
            </FormItem>
          )}
        />

        <Button type="submit" disabled={isPending}>
          {isPending ? 'Saving...' : 'Save Changes'}
        </Button>
      </form>
    </Form>
  );
}
