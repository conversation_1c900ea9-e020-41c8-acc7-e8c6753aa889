"use server";

// Use named import for prisma
import { prisma } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import fs from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { auth } from '@/auth';

// Helper function to get storage path
function getCoverStoragePath(): string {
  // Determine the base storage directory
  const storageBase = process.env.IMAGE_STORAGE_PATH || path.join(process.cwd(), 'public', 'uploads');
  // Ensure the final path includes the 'covers' subdirectory
  const coversPath = path.join(storageBase, 'covers');
  // Ensure the path is absolute for fs operations
  return path.resolve(coversPath);
}

// Helper function to get public URL path
function getCoverPublicPath(filename: string): string {
    if (process.env.IMAGE_STORAGE_PATH) {
        // If using custom storage path outside public, you'll need an API route to serve images.
        // For MVP, we assume if IMAGE_STORAGE_PATH is set, it's still mapped to be publicly accessible
        // or handled by a dedicated image serving route later.
        // This example returns a path assuming it's somehow web-accessible.
        // A more robust solution would involve an API route like /api/images/covers/[filename]
        console.warn("IMAGE_STORAGE_PATH is set, ensure images are served correctly.");
        // Adjust this path based on how you serve images from the custom path
        // Ensure this function always returns a string
        return `/uploads/covers/${filename}`; // Placeholder - adjust if needed
    } else {
        // Default path within /public
        return `/uploads/covers/${filename}`;
    }
}


/**
 * Fetch all manga series
 */
export async function getSeriesList() {
  // No auth check needed for just fetching list data usually,
  // but ensure pages calling this are protected by middleware.
  const series = await prisma.mangaSeries.findMany({
    orderBy: { title: 'asc' },
  });
  return series;
}

/**
 * Fetch a single manga series by ID, including its chapters
 */
export async function getSeriesById(id: string) {
  // No auth check needed here either, assuming calling page is protected.
  const series = await prisma.mangaSeries.findUnique({
    where: { id },
    include: {
      chapters: {
        orderBy: {
          // Assuming chapterNumber is stored as a string that can be numerically sorted
          // Adjust if chapterNumber is a number or needs different sorting
          chapterNumber: 'asc', // Or 'desc' if you want newest first
        },
      },
    },
  });
  return series;
}


/**
 * Create a new manga series
 */
export async function createSeries(formData: FormData) {
  const session = await auth(); // Check authentication
  if (!session?.user) {
    throw new Error("Not authenticated");
  }

  const title = formData.get('title')?.toString() || '';
  const description = formData.get('description')?.toString() || null;
  const status = formData.get('status')?.toString() || null;
  const author = formData.get('author')?.toString() || null;
  const artist = formData.get('artist')?.toString() || null;
  const genres = formData.get('genres')?.toString() || null;
  const coverFile = formData.get('coverImage') as File;
  let coverImagePath: string | null = null;

  // Basic validation
  if (!title) {
      throw new Error("Title is required");
  }

  if (coverFile && coverFile.size > 0) {
    // Validate file type/size here if needed

    const uploadsDir = getCoverStoragePath();
    await fs.mkdir(uploadsDir, { recursive: true });
    const ext = path.extname(coverFile.name);
    const filename = `${uuidv4()}${ext}`;
    const filePath = path.join(uploadsDir, filename);
    const buffer = Buffer.from(await coverFile.arrayBuffer());

    try {
        await fs.writeFile(filePath, buffer);
        coverImagePath = getCoverPublicPath(filename);
    } catch (error) {
        console.error("Failed to write cover image:", error);
        throw new Error("Failed to save cover image.");
    }
  }

  try {
      await prisma.mangaSeries.create({
        data: { title, description, status, author, artist, genres, coverImage: coverImagePath }
      });
      revalidatePath('/dashboard/series'); // Revalidate the series list page
      return { success: true, message: "Series created successfully." };
  } catch (error) {
      console.error("Failed to create series:", error);
      // Clean up uploaded file if DB insert fails
      if (coverImagePath) {
          const filename = path.basename(coverImagePath);
          const fullPath = path.join(getCoverStoragePath(), filename);
          try {
              await fs.unlink(fullPath);
          } catch (cleanupError) {
              // Log the cleanup error, but the main error is the DB one
              console.error(
                "Failed to clean up uploaded file after DB error:",
                cleanupError
              );
          }
      }
      // Return a failure message
      return { success: false, message: "Database error occurred while creating series." };
  }
}

/**
 * Delete a manga series
 */
export async function deleteSeries(formData: FormData) {
  const session = await auth(); // Check authentication
  if (!session?.user) {
    throw new Error("Not authenticated");
  }

  const id = formData.get('id')?.toString();
  if (!id) {
    throw new Error("ID is required for deletion");
  }

  // Optional: Get series details to delete associated cover image
  const series = await prisma.mangaSeries.findUnique({ where: { id } });
  const coverPath = series?.coverImage;

  try {
      await prisma.mangaSeries.delete({ where: { id } });

      // Delete cover image after successful DB deletion
      if (coverPath) {
          const filename = path.basename(coverPath);
          const fullPath = path.join(getCoverStoragePath(), filename);
           try {
              await fs.unlink(fullPath);
          } catch (deleteError: any) {
              // Log error only if it's not 'File not found'
              if (deleteError.code !== 'ENOENT') {
                 console.error("Failed to delete associated cover image:", deleteError);
              }
          }
      }

      revalidatePath('/dashboard/series');
      return { success: true, message: "Series deleted successfully." };
  } catch (error) {
      console.error("Failed to delete series:", error);
      // Use a more specific error message
      throw new Error("Database error: Failed to delete series.");
  }
}

/**
 * Update an existing manga series
 */
export async function updateSeries(id: string, formData: FormData) {
  const session = await auth(); // Check authentication
  if (!session?.user) {
    return { success: false, message: "Not authenticated" };
  }

  if (!id) {
    return { success: false, message: "Series ID is required for update." };
  }

  const title = formData.get('title')?.toString() || '';
  const description = formData.get('description')?.toString() || null;
  const status = formData.get('status')?.toString() || null;
  const author = formData.get('author')?.toString() || null;
  const artist = formData.get('artist')?.toString() || null;
  const genres = formData.get('genres')?.toString() || null;
  const coverFile = formData.get('coverImage') as File;

  // Basic validation
  if (!title) {
    return { success: false, message: "Title is required" };
  }

  let coverImagePath: string | undefined | null = undefined; // Use undefined to signal no change initially
  let oldCoverPath: string | null = null;
  let newFilePath: string | null = null;

  // Fetch existing series to get old cover path if needed
  const existingSeries = await prisma.mangaSeries.findUnique({ where: { id } });
  if (!existingSeries) {
    return { success: false, message: "Series not found." };
  }
  oldCoverPath = existingSeries.coverImage;

  if (coverFile && coverFile.size > 0) {
    // New file uploaded, process it
    const uploadsDir = getCoverStoragePath();
    await fs.mkdir(uploadsDir, { recursive: true });
    const ext = path.extname(coverFile.name);
    const filename = `${uuidv4()}${ext}`;
    const filePath = path.join(uploadsDir, filename);
    const buffer = Buffer.from(await coverFile.arrayBuffer());

    try {
      await fs.writeFile(filePath, buffer);
      coverImagePath = getCoverPublicPath(filename);
      newFilePath = filePath; // Keep track of the new file path for potential cleanup
    } catch (error) {
      console.error("Failed to write new cover image:", error);
      return { success: false, message: "Failed to save new cover image." };
    }
  }

  try {
    const dataToUpdate: any = {
      title,
      description,
      status,
      author,
      artist,
      genres,
    };

    // Only include coverImage in update if a new one was successfully uploaded
    if (coverImagePath !== undefined) {
      dataToUpdate.coverImage = coverImagePath;
    }

    await prisma.mangaSeries.update({
      where: { id },
      data: dataToUpdate,
    });

    // Delete old cover image AFTER successful DB update, if a new one was uploaded
    if (coverImagePath !== undefined && oldCoverPath) {
      const oldFilename = path.basename(oldCoverPath);
      const fullOldPath = path.join(getCoverStoragePath(), oldFilename);
      try {
        await fs.unlink(fullOldPath);
      } catch (deleteError: any) {
        if (deleteError.code !== 'ENOENT') {
          console.error("Failed to delete old cover image:", deleteError);
          // Don't fail the whole operation, but log it
        }
      }
    }

    revalidatePath('/dashboard/series');
    revalidatePath(`/dashboard/series/${id}/edit`); // Revalidate the edit page
    return { success: true, message: "Series updated successfully." };

  } catch (error) {
    console.error("Failed to update series:", error);

    // Clean up newly uploaded file if DB update fails
    if (newFilePath) {
      try {
        await fs.unlink(newFilePath);
      } catch (cleanupError) {
        console.error(
          "Failed to clean up newly uploaded file after DB error:",
          cleanupError
        );
      }
    }
    return { success: false, message: "Database error occurred while updating series." };
  }
}
