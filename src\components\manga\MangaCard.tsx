/**
 * MangaCard component for displaying a manga series in a grid
 */
import Link from 'next/link';
import type { MangaSeries as PrismaMangaSeries } from '@/generated/prisma'; // Use Prisma type for Pick
import { OptimizedImage } from '@/components/ui/optimized-image';

// Define the props based on the actual fields used by the component
interface MangaCardProps {
  manga: Pick<PrismaMangaSeries, 'id' | 'title' | 'coverImage' | 'author' | 'status'>;
}

export function MangaCard({ manga }: MangaCardProps) {
  return (
    <Link href={`/series/${manga.id}`} className="block group">
      <div className="bg-[#343450] rounded-lg overflow-hidden transition-all duration-200 group-hover:shadow-lg group-hover:shadow-purple-500/20 group-hover:-translate-y-1">
        <div className="relative aspect-[2/3] w-full">
          <OptimizedImage
            src={manga.coverImage}
            alt={manga.title}
            fill
            sizesType="card"
            className="transition-transform duration-300 group-hover:scale-105"
            fallbackClassName="w-full h-full bg-[#3d3d59]"
            fallbackText="No Cover"
            priority={false}
            usePlaceholder={true}
          />
        </div>

        <div className="p-3 text-white">
          <h3 className="font-semibold text-base mb-1 line-clamp-1">{manga.title}</h3>

          {manga.author && (
            <p className="text-xs text-gray-300 mb-2">
              by {manga.author}
            </p>
          )}

          {manga.status && (
            <span className="inline-block px-2 py-0.5 text-xs rounded bg-[#4a4a69] text-gray-200">
              {manga.status}
            </span>
          )}
        </div>
      </div>
    </Link>
  );
}
