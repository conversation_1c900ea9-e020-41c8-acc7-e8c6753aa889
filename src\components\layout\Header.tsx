/**
 * Header component for the Taroo application
 */
'use client';

import Link from 'next/link';
import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { SearchBar } from '@/components/search/SearchBar';

export default function Header() {
	const [isSearchOpen, setIsSearchOpen] = useState(false);
	const { status } = useSession();

	return (
		<header className="bg-[#2a2a3c] text-white shadow-md">
			<div className="container mx-auto flex h-16 items-center justify-between px-4">
				<div className="flex items-center">
					<Link href="/" className="text-xl font-bold tracking-tight">
						<span className="text-white">TAROO</span>
					</Link>
				</div>

				<nav className="flex-1 flex justify-center">
					<ul className="flex items-center space-x-8">
						<li>
							<Link href="/" className="text-sm font-medium hover:text-primary transition-colors">
								Home
							</Link>
						</li>
						<li>
							<Link href="/series" className="text-sm font-medium hover:text-primary transition-colors">
								Series
							</Link>
						</li>
						<li>
							<Link href="/popular" className="text-sm font-medium hover:text-primary transition-colors">
								Popular
							</Link>
						</li>
						<li>
							<Link href="/updates" className="text-sm font-medium hover:text-primary transition-colors">
								Updates
							</Link>
						</li>
					</ul>
				</nav>

				<div className="flex items-center space-x-4">
					{isSearchOpen ? (
						<SearchBar onClose={() => setIsSearchOpen(false)} />
					) : (
						<button
							aria-label="Search"
							className="p-2 rounded-full hover:bg-[#3d3d59] transition-colors"
							onClick={() => setIsSearchOpen(true)}
						>
							<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
								<circle cx="11" cy="11" r="8"></circle>
								<line x1="21" y1="21" x2="16.65" y2="16.65"></line>
							</svg>
						</button>
					)}
					<Link href="/search" className="md:hidden p-2 rounded-full hover:bg-[#3d3d59] transition-colors">
						<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
							<circle cx="11" cy="11" r="8"></circle>
							<line x1="21" y1="21" x2="16.65" y2="16.65"></line>
						</svg>
					</Link>
					<Link
						href="/login"
						aria-label="User account"
						className="p-2 rounded-full hover:bg-[#3d3d59] transition-colors"
						title={status === 'authenticated' ? 'Go to dashboard' : 'Login'}
					>
						<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
							<path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
							<circle cx="12" cy="7" r="4"></circle>
						</svg>
					</Link>
				</div>
			</div>
		</header>
	);
}
