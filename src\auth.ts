import NextAuth from "next-auth";
import Credentials from "next-auth/providers/credentials";
import Discord from "next-auth/providers/discord";
import { authConfig } from "./auth.config";
import { prisma } from "./lib/prisma";
import { verifyPassword } from "./utils/auth";
import { supabase } from "./lib/supabase";

// Custom logger to control console output
const logger = {
  error(error: Error) {
    // Only log certain errors or in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`[auth][error]:`, error);
    }
  },
  warn(code: string) {
    if (process.env.NODE_ENV === 'development') {
      console.warn(`[auth][warn] ${code}`);
    }
  },
  debug(code: string, metadata?: unknown) {
    if (process.env.NODE_ENV === 'development' && process.env.AUTH_DEBUG === 'true') {
      console.log(`[auth][debug] ${code}:`, metadata);
    }
  }
};

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut
} = NextAuth({
  logger,
  ...authConfig,
  providers: [
    Discord({
      clientId: process.env.AUTH_DISCORD_ID!,
      clientSecret: process.env.AUTH_DISCORD_SECRET!,
    }),
    Credentials({
      async authorize(credentials) {
        try {
          const { username, password } = credentials as {
            username: string;
            password: string;
          };

          if (!username || !password) {
            return null;
          }

          const user = await prisma.adminUser.findUnique({
            where: { username },
          });

          if (!user) {
            // User not found - don't expose this information
            return null;
          }

          // Use our bcrypt password verification
          const passwordMatch = await verifyPassword(password, user.password!);

          if (!passwordMatch) {
            // Password doesn't match - don't expose this information
            return null;
          }

          return {
            id: user.id,
            name: user.username,
            email: user.email,
          };
        } catch (error) {
          // Log the error but don't expose it to the client
          if (process.env.NODE_ENV === 'development') {
            console.error('Authentication error:', error);
          }
          return null;
        }
      },
    }),
  ],
  session: { strategy: "jwt" },
  callbacks: {
    ...authConfig.callbacks,
    async signIn({ user, account, profile }) {
      if (account?.provider === "discord") {
        try {
          if (!user.email) {
            console.error("Discord user has no email address");
            return false;
          }

          // First, check if there's already a Discord-linked admin with this provider ID
          const existingDiscordUser = await prisma.adminUser.findUnique({
            where: {
              provider_providerId: {
                provider: "discord",
                providerId: user.id || "",
              },
            },
          });

          if (existingDiscordUser) {
            // User already has Discord linked, allow sign in
            return true;
          }

          // Check if there's an existing admin with this email address
          const existingAdminByEmail = await prisma.adminUser.findUnique({
            where: { email: user.email },
          });

          if (!existingAdminByEmail) {
            // No existing admin with this email - reject authentication
            console.error(`Discord sign-in attempted by non-admin email: ${user.email}`);
            return false;
          }

          // Link Discord account to existing admin
          await prisma.adminUser.update({
            where: { id: existingAdminByEmail.id },
            data: {
              provider: "discord",
              providerId: user.id,
              image: user.image, // Update profile image from Discord
              // Preserve existing username and password for dual auth
            },
          });

          return true;
        } catch (error) {
          console.error("Error during Discord sign in:", error);
          return false;
        }
      }
      return true;
    },
    jwt: async ({ token, user, account, profile }) => {
      if (user) {
        // For Discord authentication, we need to get the admin user data
        if (account?.provider === "discord") {
          try {
            // Find the admin user by Discord provider ID or email
            const adminUser = await prisma.adminUser.findFirst({
              where: {
                OR: [
                  {
                    provider: "discord",
                    providerId: user.id,
                  },
                  {
                    email: user.email,
                  },
                ],
              },
            });

            if (adminUser) {
              token.id = adminUser.id;
              token.email = adminUser.email;
              token.image = user.image || adminUser.image;
              token.provider = "discord";
              token.originalUsername = adminUser.username || undefined; // Store original username
              token.discordUsername = (profile?.username as string) || user.name || undefined; // Discord username for display
              token.name = (profile?.username as string) || user.name || undefined; // Use Discord username for display
            }
          } catch (error) {
            console.error("Error fetching admin user in JWT callback:", error);
          }
        } else {
          // For credentials authentication
          token.id = user.id;
          token.name = user.name;
          token.email = user.email;
          token.image = user.image;
          token.provider = "credentials";
          token.originalUsername = user.name || undefined; // For credentials, name is the username
        }
      }
      return token;
    },
    session: async ({ session, token }) => {
      if (token) {
        session.user.id = token.id as string;
        session.user.name = token.name as string;
        session.user.email = token.email as string;
        session.user.image = token.image as string;

        // Add custom properties for tracking authentication method and usernames
        session.user.provider = token.provider as string;
        session.user.originalUsername = token.originalUsername as string;
        session.user.discordUsername = token.discordUsername as string;
      }
      return session;
    },
  },
});
