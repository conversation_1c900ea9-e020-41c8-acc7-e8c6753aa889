import { getAllChapters } from '@/actions/chapterActions';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ChapterWithMangaSeries } from '@/types';

export default async function ChaptersPage() {
  const chapters = await getAllChapters();

  if (!chapters) {
    return (
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">Chapters</h1>
        <p>You need to be logged in to view this page.</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-white">All Chapters</h1>
      </div>

      <div className="bg-[#343450] rounded-lg p-6 mb-8">
        <p className="text-gray-300">
          This page shows all chapters across all manga series. You can edit chapter details, manage pages, or delete chapters.
        </p>
      </div>

      {chapters.length === 0 ? (
        <div className="bg-[#2a2a3c] rounded-lg p-6 text-center">
          <p className="text-gray-300 mb-4">No chapters have been uploaded yet.</p>
          <Button asChild>
            <Link href="/dashboard/series">Go to Series Management</Link>
          </Button>
        </div>
      ) : (
        <div className="bg-[#2a2a3c] rounded-lg overflow-hidden">
          <Table>
            <TableCaption>A list of all chapters across all series.</TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[80px]">Series</TableHead>
                <TableHead>Chapter</TableHead>
                <TableHead>Title</TableHead>
                <TableHead>Pages</TableHead>
                <TableHead>Upload Date</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {chapters.map((chapter: ChapterWithMangaSeries) => (
                <TableRow key={chapter.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {chapter.mangaSeries.coverImage ? (
                        <div className="relative w-10 h-14 overflow-hidden rounded">
                          <Image
                            src={chapter.mangaSeries.coverImage}
                            alt={chapter.mangaSeries.title}
                            fill
                            sizes="40px"
                            className="object-cover"
                          />
                        </div>
                      ) : (
                        <div className="w-10 h-14 bg-gray-700 rounded flex items-center justify-center text-xs text-gray-400">
                          No Cover
                        </div>
                      )}
                      <Link 
                        href={`/dashboard/series/${chapter.mangaSeries.id}/edit`}
                        className="text-primary hover:underline line-clamp-1"
                      >
                        {chapter.mangaSeries.title}
                      </Link>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">{chapter.chapterNumber}</TableCell>
                  <TableCell>{chapter.title || '-'}</TableCell>
                  <TableCell>{chapter.pageCount || 0}</TableCell>
                  <TableCell>{new Date(chapter.uploadDate).toLocaleDateString()}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/dashboard/chapters/${chapter.id}/edit`}>View/Edit</Link>
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
}
