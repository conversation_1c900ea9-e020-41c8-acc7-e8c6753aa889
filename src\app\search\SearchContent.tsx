'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { MangaCard } from '@/components/manga/MangaCard';
import { searchManga } from '@/actions/searchActions';
import { SearchInput } from '@/components/search/SearchInput';

export function SearchContent() {
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';

  const [results, setResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch search results when query parameter changes
  useEffect(() => {
    const fetchResults = async () => {
      if (!query) {
        setResults([]);
        return;
      }

      setIsLoading(true);
      try {
        const searchResults = await searchManga(query);
        setResults(searchResults);
      } catch (error) {
        console.error('Error searching manga:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchResults();
  }, [query]);

  return (
    <div className="space-y-8">
      <div className="bg-[#343450] rounded-lg p-8 mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Search Manga</h1>
        <p className="text-gray-300 mb-6">Find your favorite manga series</p>

        <SearchInput
          defaultValue={query}
          placeholder="Search by title, author, or genre..."
        />
      </div>

      {isLoading ? (
        <div className="text-center py-8">
          <p className="text-gray-300">Loading...</p>
        </div>
      ) : (
        <>
          {query && (
            <h2 className="text-xl font-semibold mb-4">
              {results.length === 0
                ? 'No results found'
                : `Found ${results.length} result${results.length === 1 ? '' : 's'} for "${query}"`}
            </h2>
          )}

          {results.length > 0 && (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
              {results.map((manga) => (
                <MangaCard key={manga.id} manga={manga} />
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
}
