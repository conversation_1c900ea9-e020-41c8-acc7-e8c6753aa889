'use client';

import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface SeriesPaginationProps {
  currentPage: number;
  totalPages: number;
  genre?: string | null;
  status?: string | null;
  sort?: string;
  order?: string;
}

export function SeriesPagination({
  currentPage,
  totalPages,
  genre,
  status,
  sort,
  order,
}: SeriesPaginationProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Function to create URL with updated page
  const createPageUrl = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', page.toString());
    return `${pathname}?${params.toString()}`;
  };

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages: (number | string)[] = [];

    // Always show first page
    pages.push(1);

    // Calculate range around current page
    let startPage = Math.max(2, currentPage - 1);
    let endPage = Math.min(totalPages - 1, currentPage + 1);

    // Ensure we show at least 3 pages in the middle if available
    if (endPage - startPage < 2) {
      if (startPage === 2) {
        endPage = Math.min(totalPages - 1, startPage + 2);
      } else if (endPage === totalPages - 1) {
        startPage = Math.max(2, endPage - 2);
      }
    }

    // Add ellipsis if needed
    if (startPage > 2) {
      pages.push('ellipsis-start');
    }

    // Add middle pages
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    // Add ellipsis if needed
    if (endPage < totalPages - 1) {
      pages.push('ellipsis-end');
    }

    // Always show last page if more than one page
    if (totalPages > 1) {
      pages.push(totalPages);
    }

    return pages;
  };

  const pageNumbers = getPageNumbers();

  return (
    <div className="flex justify-center mt-8">
      <div className="flex items-center gap-1">
        {/* Previous Page Button */}
        <Button
          variant="outline"
          size="icon"
          disabled={currentPage === 1}
          onClick={() => router.push(createPageUrl(currentPage - 1))}
          className="bg-[#343450] border-[#4a4a69]"
        >
          <ChevronLeft className="h-4 w-4" />
          <span className="sr-only">Previous page</span>
        </Button>

        {/* Page Numbers */}
        {pageNumbers.map((page, index) => {
          if (page === 'ellipsis-start' || page === 'ellipsis-end') {
            return (
              <span key={page} className="px-2 text-gray-400">
                ...
              </span>
            );
          }

          return (
            <Button
              key={index}
              variant={currentPage === page ? "default" : "outline"}
              size="icon"
              onClick={() => router.push(createPageUrl(page as number))}
              className={currentPage === page
                ? ""
                : "bg-[#343450] border-[#4a4a69] hover:bg-[#4a4a69]"
              }
            >
              {page}
            </Button>
          );
        })}

        {/* Next Page Button */}
        <Button
          variant="outline"
          size="icon"
          disabled={currentPage === totalPages}
          onClick={() => router.push(createPageUrl(currentPage + 1))}
          className="bg-[#343450] border-[#4a4a69]"
        >
          <ChevronRight className="h-4 w-4" />
          <span className="sr-only">Next page</span>
        </Button>
      </div>
    </div>
  );
}
