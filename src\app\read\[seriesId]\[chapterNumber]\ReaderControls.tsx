'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { OptimizedImage } from '@/components/ui/optimized-image';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { useLocalStorage } from '@/hooks/useLocalStorage'; // Assuming you have this hook
import { Chapter, Page, MangaSeries } from '@/generated/prisma'; // Import types
import { ChevronLeft, ChevronRight, BookOpen, Rows } from 'lucide-react';

interface ReaderControlsProps {
	seriesId: string;
	currentChapterNumber: string;
	allChapters: Pick<Chapter, 'id' | 'chapterNumber'>[];
	pages: Pick<Page, 'id' | 'pageNumber' | 'imagePath'>[];
	prevChapter: Pick<Chapter, 'chapterNumber'> | null;
	nextChapter: Pick<Chapter, 'chapterNumber'> | null;
}

type ViewMode = 'single' | 'long';

export default function ReaderControls({
	seriesId,
	currentChapterNumber,
	allChapters,
	pages,
	prevChapter,
	nextChapter,
}: ReaderControlsProps) {
	const router = useRouter();
	const [currentPageIndex, setCurrentPageIndex] = useState(0);
	const [viewMode, setViewMode] = useLocalStorage<ViewMode>(
		'readerViewMode',
		'single'
	);

	const totalPages = pages.length;

	const goToPage = useCallback(
		(index: number) => {
			if (index >= 0 && index < totalPages) {
				setCurrentPageIndex(index);
				window.scrollTo(0, 0); // Scroll to top on page change in single view
			}
		},
		[totalPages]
	);

	const handleChapterChange = (value: string) => {
		if (value) {
			router.push(`/read/${seriesId}/${value}`);
		}
	};

	// Keyboard navigation
	useEffect(() => {
		const handleKeyDown = (event: KeyboardEvent) => {
			if (viewMode === 'single') {
				if (event.key === 'ArrowRight') {
					goToPage(currentPageIndex + 1);
				} else if (event.key === 'ArrowLeft') {
					goToPage(currentPageIndex - 1);
				}
			}
			// Add more keybindings if needed (e.g., for next/prev chapter)
		};

		window.addEventListener('keydown', handleKeyDown);
		return () => {
			window.removeEventListener('keydown', handleKeyDown);
		};
	}, [currentPageIndex, goToPage, viewMode]);

	// Reset to first page when chapter changes
	useEffect(() => {
		setCurrentPageIndex(0);
	}, [currentChapterNumber]);

	// We no longer need this function as the OptimizedImage component handles paths
	// through the imageUtils utility

	return (
		<div className="flex flex-col items-center">
			{/* Top Navigation/Controls */}
			<div className="mb-4 flex w-full flex-col items-center gap-2 rounded-md border bg-card p-4 md:flex-row md:justify-between">
				{/* Chapter Navigation */}
				<div className="flex items-center gap-2">
					<Button
						asChild
						variant="outline"
						size="icon"
						disabled={!prevChapter}
					>
						<Link
							href={prevChapter ? `/read/${seriesId}/${prevChapter.chapterNumber}` : '#'}
							aria-disabled={!prevChapter}
							tabIndex={!prevChapter ? -1 : undefined}
							className={!prevChapter ? 'pointer-events-none' : ''}
						>
							<ChevronLeft className="h-4 w-4" />
						</Link>
					</Button>
					<Select
						onValueChange={handleChapterChange}
						defaultValue={currentChapterNumber}
					>
						<SelectTrigger className="w-[180px]">
							<SelectValue placeholder="Select Chapter" />
						</SelectTrigger>
						<SelectContent>
							{allChapters.map((chap) => (
								<SelectItem key={chap.id} value={chap.chapterNumber}>
									Chapter {chap.chapterNumber}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
					<Button
						asChild
						variant="outline"
						size="icon"
						disabled={!nextChapter}
					>
						<Link
							href={nextChapter ? `/read/${seriesId}/${nextChapter.chapterNumber}` : '#'}
							aria-disabled={!nextChapter}
							tabIndex={!nextChapter ? -1 : undefined}
							className={!nextChapter ? 'pointer-events-none' : ''}
						>
							<ChevronRight className="h-4 w-4" />
						</Link>
					</Button>
				</div>

				{/* View Mode Toggle */}
				<Button
					variant="outline"
					size="icon"
					onClick={() => setViewMode(viewMode === 'single' ? 'long' : 'single')}
					title={viewMode === 'single' ? 'Switch to Long Strip' : 'Switch to Single Page'}
				>
					{viewMode === 'single' ? (
						<Rows className="h-4 w-4" />
					) : (
						<BookOpen className="h-4 w-4" />
					)}
				</Button>
			</div>

			{/* Reader Content Area */}
			<div className="w-full max-w-4xl">
				{viewMode === 'single' && pages[currentPageIndex] && (
					<div className="relative mb-4 flex flex-col items-center">
						{/* Clickable areas for navigation */}
						<div className="relative">
							<OptimizedImage
								key={pages[currentPageIndex].id} // Ensure re-render on page change
								src={pages[currentPageIndex].imagePath}
								alt={`Page ${currentPageIndex + 1}`}
								width={800}
								height={1200}
								className="h-auto w-full select-none"
								priority={currentPageIndex < 3}
								type="page"
								sizesType="fullWidth"
								objectFit="contain"
							/>
							<div
								className="absolute left-0 top-0 h-full w-1/3 cursor-pointer"
								onClick={() => goToPage(currentPageIndex - 1)}
								title="Previous Page (Left Arrow)"
							/>
							<div
								className="absolute right-0 top-0 h-full w-1/3 cursor-pointer"
								onClick={() => goToPage(currentPageIndex + 1)}
								title="Next Page (Right Arrow)"
							/>
						</div>

						{/* Page Number Indicator */}
						<div className="mt-2 text-sm text-muted-foreground">
							Page {currentPageIndex + 1} / {totalPages}
						</div>
					</div>
				)}

				{viewMode === 'long' && (
					<div className="flex flex-col items-center gap-0">
						{pages.map((page, index) => (
							<OptimizedImage
								key={page.id}
								src={page.imagePath}
								alt={`Page ${index + 1}`}
								width={800}
								height={1200}
								className="h-auto w-full select-none mb-1"
								priority={index < 3}
								type="page"
								sizesType="fullWidth"
								objectFit="contain"
								usePlaceholder={false}
							/>
						))}
					</div>
				)}
			</div>

			{/* Bottom Navigation (Single Page Mode) */}
			{viewMode === 'single' && (
				<div className="mt-4 flex w-full max-w-md justify-between">
					<Button
						variant="outline"
						onClick={() => goToPage(currentPageIndex - 1)}
						disabled={currentPageIndex === 0}
					>
						<ChevronLeft className="mr-2 h-4 w-4" /> Previous Page
					</Button>
					<Button
						variant="outline"
						onClick={() => goToPage(currentPageIndex + 1)}
						disabled={currentPageIndex === totalPages - 1}
					>
						Next Page <ChevronRight className="ml-2 h-4 w-4" />
					</Button>
				</div>
			)}
		</div>
	);
}
