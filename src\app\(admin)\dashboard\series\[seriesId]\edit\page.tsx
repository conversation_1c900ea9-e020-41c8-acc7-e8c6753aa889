import { getSeriesById } from '@/actions/seriesActions';
import { SeriesForm } from "../../SeriesForm"
import { notFound } from 'next/navigation';
import { ChapterUploadForm } from './ChapterUploadForm'; // Import the new form
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from '@/components/ui/button';
import Link from 'next/link';

interface EditSeriesPageProps {
  params: Promise<{
    seriesId: string;
  }>;
}

// Use await on params before destructuring
export default async function EditSeriesPage({ params }: EditSeriesPageProps) {
  const { seriesId } = await params;
  // Fetch series data, now including chapters
  const series = await getSeriesById(seriesId);

  if (!series) {
    notFound();
  }

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-2xl font-bold mb-4">Edit Manga Series: {series.title}</h1>
        <SeriesForm initialData={series} />
      </div>

      <hr />

      {/* Chapter List Section */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Chapters</h2>
        {series.chapters && series.chapters.length > 0 ? (
          <Table>
            <TableCaption>A list of chapters for this series.</TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">Number</TableHead>
                <TableHead>Title</TableHead>
                <TableHead>Upload Date</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {series.chapters.map((chapter) => (
                <TableRow key={chapter.id}>
                  <TableCell className="font-medium">{chapter.chapterNumber}</TableCell>
                  <TableCell>{chapter.title || '-'}</TableCell>
                  <TableCell>{chapter.createdAt.toLocaleDateString()}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="outline" size="sm" asChild>
                      {/* Activate the link to the chapter edit page */}
                      <Link href={`/dashboard/chapters/${chapter.id}/edit`}>View/Edit</Link>
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <p>No chapters uploaded yet.</p>
        )}
      </div>

      <hr />

      {/* Chapter Upload Section */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Upload New Chapter</h2>
        <ChapterUploadForm mangaSeriesId={series.id} />
      </div>
    </div>
  );
}
