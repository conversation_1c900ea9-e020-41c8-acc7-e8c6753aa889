// src/app/contact/page.tsx
import React from 'react';

export default function ContactPage() {
	return (
		<div className="container mx-auto px-4 py-8">
			<div className="max-w-3xl mx-auto bg-[#343450] rounded-lg p-8 shadow-lg">
				<h1 className="mb-6 text-3xl font-bold text-white">Contact Us</h1>
				<div className="space-y-4 text-gray-200">
					<p>
						This is a placeholder contact page for the Taroo MVP.
					</p>
					<p>
						In a real application, you might include a contact form or contact
						information here.
					</p>

					<div className="mt-8 p-6 bg-[#2a2a3c] rounded-lg">
						<h2 className="text-xl font-semibold mb-4 text-white">Get in Touch</h2>
						<div className="flex flex-col space-y-2">
							<div className="flex items-center">
								<svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
								</svg>
								<span><EMAIL></span>
							</div>
							<div className="flex items-center">
								<svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
								</svg>
								<span>+1 (555) 123-4567</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
