'use client';

import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, ControllerRenderProps, FieldValues } from 'react-hook-form'; // Import types
import { <PERSON><PERSON> } from "@/components/ui/button"; // Corrected path casing
// Ensure Form components are imported correctly
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"; // Corrected path
// Ensure Input is imported correctly
import { Input } from "@/components/ui/input"; // Corrected path
// Ensure Textarea is imported correctly
import { Textarea } from '@/components/ui/textarea'; // Corrected path
// Replace useToast with sonner
import { toast } from "sonner";
import { useRouter } from 'next/navigation';
import { MangaSeries } from '@/generated/prisma';
import { createSeries, updateSeries } from '@/actions/seriesActions';
import { useState, ChangeEvent } from 'react'; // Import ChangeEvent
import { OptimizedImage } from '@/components/ui/optimized-image';

// Define Zod schema for validation
const formSchema = z.object({
  title: z.string().min(1, { message: "Title is required." }),
  description: z.string().optional().nullable(), // Allow null
  status: z.string().optional().nullable(),      // Allow null
  author: z.string().optional().nullable(),      // Allow null
  artist: z.string().optional().nullable(),      // Allow null
  genres: z.string().optional().nullable(),      // Allow null
  // Allow File, undefined, or null. Check for File instance in submit handler.
  coverImage: z.any().optional().nullable(),
});

type SeriesFormValues = z.infer<typeof formSchema>;

interface SeriesFormProps {
  initialData?: MangaSeries | null; // Make initialData optional and nullable
}

export function SeriesForm({ initialData }: SeriesFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<SeriesFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: initialData?.title || '',
      description: initialData?.description || '',
      status: initialData?.status || '',
      author: initialData?.author || '',
      artist: initialData?.artist || '',
      genres: initialData?.genres || '',
      coverImage: undefined, // File input cannot have default value programmatically for security reasons
    },
  });

  const onSubmit = async (values: SeriesFormValues) => {
    setIsSubmitting(true);

    const promise = () => new Promise(async (resolve, reject) => {
        const formData = new FormData();
        formData.append('title', values.title);
        // Check for null before appending optional fields
        if (values.description) formData.append('description', values.description);
        if (values.status) formData.append('status', values.status);
        if (values.author) formData.append('author', values.author);
        if (values.artist) formData.append('artist', values.artist);
        if (values.genres) formData.append('genres', values.genres);
        // Explicitly check if coverImage is a File instance before appending
        if (values.coverImage instanceof File && values.coverImage.size > 0) {
          formData.append('coverImage', values.coverImage);
        }

        // ... rest of submit logic ...
        let result;
        if (initialData) {
          result = await updateSeries(initialData.id, formData);
        } else {
          result = await createSeries(formData);
        }

        if (result.success) {
          resolve(result.message);
          router.push('/dashboard/series');
          router.refresh();
        } else {
          reject(new Error(result.message || 'An unknown error occurred'));
        }
    });

    toast.promise(promise(), {
        loading: initialData ? 'Updating series...' : 'Creating series...',
        success: (message) => `${message}`,
        error: (error) => `${error.message || 'Operation failed.'}`,
        finally: () => setIsSubmitting(false)
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="title"
          // Remove explicit type for field, let TS infer
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title *</FormLabel>
              <FormControl>
                <Input placeholder="Enter series title" {...field} disabled={isSubmitting} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
           // Remove explicit type for field, let TS infer
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea placeholder="Enter series description" {...field} value={field.value ?? ''} disabled={isSubmitting} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="status"
           // Remove explicit type for field, let TS infer
          render={({ field }) => (
            <FormItem>
              <FormLabel>Status</FormLabel>
              <FormControl>
                {/* Consider using a Select component here later */}
                <Input placeholder="e.g., Ongoing, Completed" {...field} value={field.value ?? ''} disabled={isSubmitting} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="author"
           // Remove explicit type for field, let TS infer
          render={({ field }) => (
            <FormItem>
              <FormLabel>Author</FormLabel>
              <FormControl>
                <Input placeholder="Enter author name" {...field} value={field.value ?? ''} disabled={isSubmitting} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="artist"
           // Remove explicit type for field, let TS infer
          render={({ field }) => (
            <FormItem>
              <FormLabel>Artist</FormLabel>
              <FormControl>
                <Input placeholder="Enter artist name" {...field} value={field.value ?? ''} disabled={isSubmitting} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="genres"
           // Remove explicit type for field, let TS infer
          render={({ field }) => (
            <FormItem>
              <FormLabel>Genres</FormLabel>
              <FormControl>
                <Input placeholder="Comma-separated genres" {...field} value={field.value ?? ''} disabled={isSubmitting} />
              </FormControl>
              <FormDescription>
                Simple comma-separated list for now (e.g., Action, Adventure, Fantasy).
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
         <FormField
          control={form.control}
          name="coverImage"
           // Remove explicit type for field, let TS infer. Keep types for event handler.
          render={({ field: { onChange, value, ...rest } }) => (
            <FormItem>
              <FormLabel>Cover Image</FormLabel>
              {initialData?.coverImage && (
                <div className="mb-2">
                  <p className="text-sm text-muted-foreground">Current Cover:</p>
                  <div className="relative h-20 w-14 overflow-hidden rounded">
                    <OptimizedImage
                      src={initialData.coverImage}
                      alt="Current cover"
                      fill
                      sizesType="thumbnail"
                      className="rounded"
                    />
                  </div>
                </div>
              )}
              <FormControl>
                <Input
                  type="file"
                  accept="image/*"
                  // Extract the file from the event and pass it to react-hook-form's onChange
                  onChange={(event: ChangeEvent<HTMLInputElement>) => {
                    onChange(event.target.files?.[0] ?? null); // Pass the File object or null
                  }}
                  // We don't need to bind value for file inputs
                  {...rest}
                  disabled={isSubmitting}
                />
              </FormControl>
              <FormDescription>
                {initialData ? 'Upload a new image to replace the current one.' : 'Upload the cover image.'}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (initialData ? 'Updating...' : 'Creating...') : (initialData ? 'Update Series' : 'Create Series')}
        </Button>
        <Button type="button" variant="outline" onClick={() => router.back()} disabled={isSubmitting} className="ml-2">
          Cancel
        </Button>
      </form>
    </Form>
  );
}
