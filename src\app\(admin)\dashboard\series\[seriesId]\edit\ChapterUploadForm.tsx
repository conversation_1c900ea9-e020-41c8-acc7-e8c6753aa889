'use client';

import { useActionState } from 'react';
import { useFormStatus } from 'react-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { createChapter } from '@/actions/chapterActions';
import { CreateChapterSchema } from '@/lib/schemas';
import { useEffect, useRef } from 'react';
import { toast } from "sonner";

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";

const ClientSchema = CreateChapterSchema.omit({ pages: true });

interface ChapterUploadFormProps {
  mangaSeriesId: string;
}

export function ChapterUploadForm({ mangaSeriesId }: ChapterUploadFormProps) {
  const initialState = { message: null, errors: {} };
  const [state, formAction, isPending] = useActionState(createChapter, initialState);
  const formRef = useRef<HTMLFormElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const form = useForm<z.infer<typeof ClientSchema>>({
    resolver: zodResolver(ClientSchema),
    defaultValues: {
      mangaSeriesId: mangaSeriesId,
      chapterNumber: '',
      title: '',
    },
  });

  useEffect(() => {
    if (state?.message) {
      if (state.errors && Object.keys(state.errors).length > 0) {
        toast.error("Upload Failed", {
          description: state.message || "Please check the form for errors.",
        });
        if (state.errors.chapterNumber) {
          form.setError('chapterNumber', { message: state.errors.chapterNumber[0] });
        }
        if (state.errors.title) {
          form.setError('title', { message: state.errors.title[0] });
        }
      } else {
        toast.success("Upload Successful", {
          description: state.message,
        });
        form.reset();
        formRef.current?.reset();
        if (fileInputRef.current) {
            fileInputRef.current.value = ''; // Corrected variable name
        }
      }
    }
  }, [state, form]);

  return (
    <Form {...form}>
      <form
        ref={formRef}
        action={formAction} // Corrected action prop
        className="space-y-6"
      >
        <input type="hidden" name="mangaSeriesId" value={mangaSeriesId} />

        {state?.errors?._form && (
          <div className="text-sm font-medium text-destructive">
            {state.errors._form.join(', ')}
          </div>
        )}

        <FormField
          control={form.control}
          name="chapterNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Chapter Number *</FormLabel>
              <FormControl>
                <Input placeholder="e.g., 1, 10.5, Extra" {...field} required />
              </FormControl>
              <FormMessage />
              {state?.errors?.chapterNumber && !form.formState.errors.chapterNumber && (
                 <p className="text-sm font-medium text-destructive">{state.errors.chapterNumber.join(', ')}</p>
              )}
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title (Optional)</FormLabel>
              <FormControl>
                <Input placeholder="e.g., The Beginning" {...field} />
              </FormControl>
              <FormMessage />
              {state?.errors?.title && !form.formState.errors.title && (
                 <p className="text-sm font-medium text-destructive">{state.errors.title.join(', ')}</p>
              )}
            </FormItem>
          )}
        />

        <FormItem>
          <FormLabel>Page Images *</FormLabel>
          <FormControl>
            <Input
              ref={fileInputRef}
              id="pages-input"
              name="pages"
              type="file"
              multiple
              required
              accept="image/*"
            />
          </FormControl>
           <FormDescription>
             Select one or more page images (e.g., .jpg, .png, .webp). Files will be sorted by name.
           </FormDescription>
           {state?.errors?.pages && (
             <p className="text-sm font-medium text-destructive">{state.errors.pages.join(', ')}</p>
           )}
        </FormItem>

        <SubmitButton />
      </form>
    </Form>
  );
}

function SubmitButton() {
  const { pending } = useFormStatus();
  return (
    <Button type="submit" disabled={pending}>
      {pending ? 'Uploading...' : 'Upload Chapter'}
    </Button>
  );
}
