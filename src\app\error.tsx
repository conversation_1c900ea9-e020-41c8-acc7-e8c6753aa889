'use client';

import { useEffect } from 'react';
import { DatabaseError } from '@/components/ui/database-error';

export default function ErrorPage({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to the console
    console.error('Application error:', error);
  }, [error]);

  // Check if it's a database connection error
  const isDatabaseError = 
    error.message.includes("Can't reach database server") || 
    error.message.includes("database") || 
    error.name === 'DatabaseConnectionError' ||
    error.message.includes("prisma");

  if (isDatabaseError) {
    return (
      <DatabaseError 
        message="Cannot connect to the database. Please make sure your database server is running." 
      />
    );
  }

  // Default error UI for other types of errors
  return (
    <div className="flex flex-col items-center justify-center min-h-[50vh] p-6">
      <div className="max-w-md w-full bg-[#343450] rounded-lg p-8 shadow-lg text-center">
        <h2 className="text-2xl font-bold mb-4 text-white">Something went wrong!</h2>
        <p className="mb-6 text-gray-300">
          {error.message || "An unexpected error occurred"}
        </p>
        <button
          onClick={reset}
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
        >
          Try again
        </button>
      </div>
    </div>
  );
}
