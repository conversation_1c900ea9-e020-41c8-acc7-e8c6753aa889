import { getChapterById } from '@/actions/chapterActions';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { OptimizedImage } from '@/components/ui/optimized-image';
import { ChapterEditForm } from './ChapterEditForm';
import { DeleteChapterButton } from './DeleteChapterButton';
import { DeletePageButton } from './DeletePageButton';
import { ReorderPagesButton } from './ReorderPagesButton';

interface EditChapterPageProps {
  params: Promise<{
    chapterId: string;
  }>;
}

// Use await on params before destructuring
export default async function EditChapterPage({ params }: EditChapterPageProps) {
  const { chapterId } = await params;
  const chapter = await getChapterById(chapterId);

  if (!chapter) {
    notFound();
  }

  return (
    <div className="space-y-8">
      <Button variant="outline" asChild>
        <Link href={`/dashboard/series/${chapter.mangaSeriesId}/edit`}>
          &larr; Back to Series: {chapter.mangaSeries.title}
        </Link>
      </Button>

      <h1 className="text-2xl font-bold">Edit Chapter: {chapter.chapterNumber}</h1>

      {/* Section 1: Edit Details Form */}
      <section>
        <h2 className="text-xl font-semibold mb-4">Chapter Details</h2>
        <ChapterEditForm chapter={chapter} />
      </section>

      <hr />

      {/* Section 2: Manage Pages (Placeholder) */}
      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Manage Pages ({chapter.pages.length})</h2>
          {chapter.pages.length > 1 && (
            <ReorderPagesButton chapterId={chapterId} pages={chapter.pages} />
          )}
        </div>
        <div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-4">
          {chapter.pages.map((page) => (
            <div key={page.id} className="border p-2 rounded text-center text-sm">
              {/* Basic display - needs image serving setup */}
              <div className="bg-muted h-24 flex items-center justify-center mb-2 relative">
                <OptimizedImage
                  src={page.imagePath}
                  alt={`Page ${page.pageNumber}`}
                  fill
                  sizesType="thumbnail"
                  className="object-contain"
                  fallbackText={`Page ${page.pageNumber}`}
                  fallbackClassName="text-muted-foreground"
                  type="page"
                  usePlaceholder={false}
                />
              </div>
              <div className="flex items-center justify-between">
                <span>Page {page.pageNumber}</span>
                <DeletePageButton pageId={page.id} pageNumber={page.pageNumber} />
              </div>
            </div>
          ))}
        </div>
        {/* Add Upload New Pages button/form here later */}
      </section>

      <hr />

      {/* Section 3: Delete Chapter (Placeholder) */}
      <section>
         <h2 className="text-xl font-semibold mb-4 text-destructive">Danger Zone</h2>
         <DeleteChapterButton chapterId={chapterId} chapterNumber={chapter.chapterNumber} />
         <p className="text-sm text-muted-foreground mt-2">This action cannot be undone.</p>
      </section>
    </div>
  );
}
