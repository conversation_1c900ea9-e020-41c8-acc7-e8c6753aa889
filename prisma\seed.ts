import { PrismaClient } from '../src/generated/prisma';
import { hash } from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
    // Remove any existing admin users to ensure proper seeding with bcrypt
    await prisma.adminUser.deleteMany();
    console.log('Deleted existing admin users');
    // Check if admin user already exists
    const existingAdmin = await prisma.adminUser.findUnique({
        where: { username: 'admin' },
    });

    if (!existingAdmin) {
        // Create admin user if it doesn't exist
        const hashedPassword = await hash('admin123', 10); // Default password, should be changed after first login

        const admin = await prisma.adminUser.create({
            data: {
                username: 'admin',
                password: hashedPassword,
            },
        });

        console.log(`Created admin user with id: ${admin.id}`);
    } else {
        console.log('Admin user already exists, skipping creation');
    }
}

main()
    .catch((e) => {
        console.error(e);
        process.exit(1);
    })
    .finally(async () => {
        await prisma.$disconnect();
    });
