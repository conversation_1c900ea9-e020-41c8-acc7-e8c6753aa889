import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

/**
 * API route to serve images from the custom storage path
 * This is a fallback in case the static file serving doesn't work
 */
export async function GET(request: NextRequest) {
  try {
    // Get the image path from the URL query parameter
    const url = new URL(request.url);
    const imagePath = url.searchParams.get('path');
    
    if (!imagePath) {
      return new NextResponse('Image path is required', { status: 400 });
    }
    
    // Determine the storage base path
    const storageBasePath = process.env.IMAGE_STORAGE_PATH || path.join(process.cwd(), 'public', 'uploads');
    
    // Construct the full path to the image
    const fullPath = path.join(storageBasePath, imagePath);
    
    // Check if the file exists
    try {
      await fs.access(fullPath);
    } catch (error) {
      return new NextResponse('Image not found', { status: 404 });
    }
    
    // Read the file
    const fileBuffer = await fs.readFile(fullPath);
    
    // Determine the content type based on file extension
    const ext = path.extname(fullPath).toLowerCase();
    let contentType = 'application/octet-stream';
    
    if (ext === '.jpg' || ext === '.jpeg') contentType = 'image/jpeg';
    else if (ext === '.png') contentType = 'image/png';
    else if (ext === '.gif') contentType = 'image/gif';
    else if (ext === '.webp') contentType = 'image/webp';
    else if (ext === '.svg') contentType = 'image/svg+xml';
    
    // Return the image with the appropriate content type
    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000, immutable',
      },
    });
  } catch (error) {
    console.error('Error serving image:', error);
    return new NextResponse('Error serving image', { status: 500 });
  }
}
