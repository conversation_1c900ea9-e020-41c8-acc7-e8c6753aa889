import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import Header from '@/components/layout/Header'; // Ensure correct import path
import { Footer } from '@/components/layout/Footer'; // Fix: Use named import
import { Toaster } from '@/components/ui/sonner'; // Keep Toaster for notifications
import { SessionProvider } from '@/components/providers/SessionProvider';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Taroo - Manga Reader',
  description: 'Read your favorite manga series online.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.className} flex flex-col min-h-screen bg-[#2a2a3c] text-white`}>
        <SessionProvider>
          <Header />
          <main className="flex-grow container mx-auto px-4 py-8">
            {children}
          </main>
          <Footer />
          <Toaster /> {/* Keep Toaster accessible globally */}
        </SessionProvider>
      </body>
    </html>
  );
}
