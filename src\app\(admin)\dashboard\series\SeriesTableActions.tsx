'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
// Ensure Dialog components are imported correctly
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog'; // Corrected path
import { deleteSeries } from '@/actions/seriesActions';
// Replace useToast with sonner
import { toast } from "sonner";
import { MangaSeries } from '@/generated/prisma'; // Import the type

interface SeriesTableActionsProps {
  series: MangaSeries;
}

export function SeriesTableActions({ series }: SeriesTableActionsProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    setIsDeleting(true);
    const promise = () => new Promise(async (resolve, reject) => {
        const formData = new FormData();
        formData.append('id', series.id);
        const result = await deleteSeries(formData);
        if (result.success) {
            resolve(result.message);
        } else {
            reject(new Error(result.message || 'Failed to delete series'));
        }
    });

    toast.promise(promise(), {
        loading: 'Deleting series...',
        success: (message) => `${message}`,
        error: (error) => `${error.message || 'Could not delete series.'}`,
        finally: () => setIsDeleting(false)
    });
  };

  return (
    <div className="flex space-x-2">
      <Link href={`/dashboard/series/${series.id}/edit`}>
        <Button variant="outline" size="sm">Edit</Button>
      </Link>
      <Dialog>
        <DialogTrigger asChild>
          {/* Use correct variant for destructive button */}
          <Button variant="destructive" size="sm">Delete</Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you sure?</DialogTitle>
            <DialogDescription>
              This action cannot be undone. This will permanently delete the series
              "<strong>{series.title}</strong>" and all associated chapters and pages.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" disabled={isDeleting}>Cancel</Button>
            </DialogClose>
            <Button
              variant="destructive" // Use correct variant
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
