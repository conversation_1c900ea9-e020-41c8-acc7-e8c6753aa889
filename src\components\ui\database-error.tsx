'use client';

import { AlertTriangle } from 'lucide-react';
import Link from 'next/link';
import { Button } from './button';

interface DatabaseErrorProps {
  message?: string;
}

export function DatabaseError({ message = 'Database connection error' }: DatabaseErrorProps) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[50vh] p-6">
      <div className="max-w-md w-full bg-[#343450] rounded-lg p-8 shadow-lg text-center">
        <div className="flex justify-center mb-4">
          <AlertTriangle className="h-12 w-12 text-destructive" />
        </div>
        <h2 className="text-2xl font-bold mb-4 text-white">Database Connection Error</h2>
        <p className="mb-6 text-gray-300">
          {message}
        </p>
        <p className="mb-6 text-sm text-gray-400">
          Please make sure your database server is running at <code className="bg-[#2a2a3c] px-2 py-1 rounded">localhost:5432</code>
        </p>
        <div className="flex justify-center space-x-4">
          <Button variant="outline" asChild>
            <Link href="/about">About</Link>
          </Button>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </div>
    </div>
  );
}
