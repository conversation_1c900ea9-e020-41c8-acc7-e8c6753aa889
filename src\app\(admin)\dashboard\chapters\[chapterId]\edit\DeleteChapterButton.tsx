'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { deleteChapter } from '@/actions/chapterActions';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';

interface DeleteChapterButtonProps {
  chapterId: string;
  chapterNumber: string;
}

export function DeleteChapterButton({ chapterId, chapterNumber }: DeleteChapterButtonProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();

  const handleDelete = async () => {
    setIsDeleting(true);
    
    const promise = () => new Promise(async (resolve, reject) => {
      const formData = new FormData();
      formData.append('chapterId', chapterId);
      
      const result = await deleteChapter({
        success: false,
        message: '',
      }, formData);
      
      if (result.success) {
        resolve(result.message);
        // Navigate back to the series page after successful deletion
        router.back();
      } else {
        reject(new Error(result.message));
      }
    });

    toast.promise(promise(), {
      loading: 'Deleting chapter...',
      success: (message) => `${message}`,
      error: (error) => `${error.message || 'Failed to delete chapter.'}`,
      finally: () => setIsDeleting(false),
    });
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="destructive">Delete Chapter</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Are you sure?</DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete Chapter {chapterNumber} 
            and all its pages.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline" disabled={isDeleting}>Cancel</Button>
          </DialogClose>
          <Button 
            variant="destructive" 
            onClick={handleDelete} 
            disabled={isDeleting}
          >
            {isDeleting ? 'Deleting...' : 'Delete Chapter'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
