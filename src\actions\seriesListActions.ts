"use server";

import { prisma } from "@/lib/prisma";
import type { MangaSeries } from "@/generated/prisma";

export type SeriesFilter = {
  genre?: string | null;
  status?: string | null;
  sortBy?: 'title' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  pageSize?: number;
};

/**
 * Fetch manga series with optional filtering and pagination
 */
export async function getFilteredSeries({
  genre,
  status,
  sortBy = 'title',
  sortOrder = 'asc',
  page = 1,
  pageSize = 24,
}: SeriesFilter = {}) {
  // Calculate pagination
  const skip = (page - 1) * pageSize;

  // Build the where clause based on filters
  const where: any = {};

  if (genre) {
    where.genres = { contains: genre, mode: "insensitive" };
  }

  if (status) {
    where.status = { equals: status, mode: "insensitive" };
  }

  // Get total count for pagination
  const totalCount = await prisma.mangaSeries.count({ where });

  // Fetch the series with filters
  const series = await prisma.mangaSeries.findMany({
    where,
    select: {
      id: true,
      title: true,
      coverImage: true,
      author: true,
      status: true,
      genres: true,
    },
    orderBy: {
      [sortBy]: sortOrder,
    },
    skip,
    take: pageSize,
  });

  // Calculate total pages
  const totalPages = Math.ceil(totalCount / pageSize);

  return {
    series,
    pagination: {
      currentPage: page,
      totalPages,
      totalItems: totalCount,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    }
  };
}

/**
 * Get all unique genres from the database
 */
export async function getAllGenres() {
  const series = await prisma.mangaSeries.findMany({
    where: {
      genres: { not: null }
    },
    select: {
      genres: true,
    },
  });

  // Extract all genres and flatten them
  const allGenres = series
    .map(s => s.genres?.split(',').map(g => g.trim()).filter(Boolean) || [])
    .flat();

  // Remove duplicates and sort
  return [...new Set(allGenres)].sort();
}

/**
 * Get all unique statuses from the database
 */
export async function getAllStatuses() {
  const statuses = await prisma.mangaSeries.findMany({
    where: {
      status: { not: null }
    },
    select: {
      status: true,
    },
    distinct: ['status'],
  });

  // Filter out null values and cast to string array
  const filteredStatuses = statuses
    .map(s => s.status)
    .filter((status): status is string => status !== null && status !== undefined);

  return filteredStatuses;
}
