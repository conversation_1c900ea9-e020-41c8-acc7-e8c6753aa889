/**
 * Footer component for the Taroo application
 */
import Link from 'next/link';

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-[#232334] py-6 mt-8 text-gray-300">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0">
            <div className="flex items-center">
              <span className="font-bold mr-2">TAROO</span>
            </div>
            <p className="text-sm text-gray-400 mt-2">
              &copy; {currentYear} Taroo. All rights reserved.
            </p>
          </div>

          <div className="flex space-x-6">
            <Link href="/about" className="text-sm text-gray-300 hover:text-primary transition-colors">
              About
            </Link>
            <Link href="/contact" className="text-sm text-gray-300 hover:text-primary transition-colors">
              Contact
            </Link>
            <Link href="/privacy" className="text-sm text-gray-300 hover:text-primary transition-colors">
              Privacy Policy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
