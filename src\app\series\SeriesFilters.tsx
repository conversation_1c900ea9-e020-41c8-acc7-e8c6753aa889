'use client';

import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';

interface SeriesFiltersProps {
  genres: string[];
  statuses: string[];
  currentGenre?: string | null;
  currentStatus?: string | null;
  currentSort: 'title' | 'createdAt';
  currentOrder: 'asc' | 'desc';
}

export function SeriesFilters({
  genres,
  statuses,
  currentGenre,
  currentStatus,
  currentSort,
  currentOrder,
}: SeriesFiltersProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Function to update URL with new search params
  const createQueryString = (name: string, value: string | null) => {
    const params = new URLSearchParams(searchParams.toString());

    if (value === null) {
      params.delete(name);
    } else {
      params.set(name, value);
    }

    // Reset to page 1 when filters change
    if (name !== 'page') {
      params.delete('page');
    }

    return params.toString();
  };

  // Handle filter changes
  const handleGenreChange = (value: string) => {
    // If 'all' is selected, remove the genre filter
    if (value === 'all') {
      router.push(`${pathname}?${createQueryString('genre', null)}`);
    } else {
      router.push(`${pathname}?${createQueryString('genre', value)}`);
    }
  };

  const handleStatusChange = (value: string) => {
    // If 'all' is selected, remove the status filter
    if (value === 'all') {
      router.push(`${pathname}?${createQueryString('status', null)}`);
    } else {
      router.push(`${pathname}?${createQueryString('status', value)}`);
    }
  };

  const handleSortChange = (value: string) => {
    const [sort, order] = value.split('-');

    const params = new URLSearchParams(searchParams.toString());
    if (sort) params.set('sort', sort);
    if (order) params.set('order', order);
    params.delete('page');

    router.push(`${pathname}?${params.toString()}`);
  };

  const clearFilters = () => {
    router.push(pathname);
  };

  // Determine if any filters are active
  const hasActiveFilters = currentGenre || currentStatus;

  // Get current sort value for select
  const sortValue = `${currentSort}-${currentOrder}`;

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4 items-center">
        {/* Genre Filter */}
        <div className="w-full sm:w-auto">
          <Select value={currentGenre || 'all'} onValueChange={handleGenreChange}>
            <SelectTrigger className="w-full sm:w-[200px]">
              <SelectValue placeholder="Genre" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Genres</SelectItem>
              {genres.map((genre) => (
                <SelectItem key={genre} value={genre}>
                  {genre}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Status Filter */}
        <div className="w-full sm:w-auto">
          <Select value={currentStatus || 'all'} onValueChange={handleStatusChange}>
            <SelectTrigger className="w-full sm:w-[200px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              {statuses.map((status) => (
                <SelectItem key={status} value={status}>
                  {status}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Sort Options */}
        <div className="w-full sm:w-auto ml-auto">
          <Select value={sortValue} onValueChange={handleSortChange}>
            <SelectTrigger className="w-full sm:w-[200px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="title-asc">Title (A-Z)</SelectItem>
              <SelectItem value="title-desc">Title (Z-A)</SelectItem>
              <SelectItem value="createdAt-desc">Newest First</SelectItem>
              <SelectItem value="createdAt-asc">Oldest First</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Active Filters */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 items-center">
          <span className="text-sm text-gray-400">Active filters:</span>

          {currentGenre && (
            <Badge variant="outline" className="flex items-center gap-1 bg-[#343450]">
              Genre: {currentGenre}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => router.push(`${pathname}?${createQueryString('genre', null)}`)}
              >
                <X className="h-3 w-3" />
                <span className="sr-only">Remove genre filter</span>
              </Button>
            </Badge>
          )}

          {currentStatus && (
            <Badge variant="outline" className="flex items-center gap-1 bg-[#343450]">
              Status: {currentStatus}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => router.push(`${pathname}?${createQueryString('status', null)}`)}
              >
                <X className="h-3 w-3" />
                <span className="sr-only">Remove status filter</span>
              </Button>
            </Badge>
          )}

          <Button
            variant="ghost"
            size="sm"
            className="text-xs ml-auto"
            onClick={clearFilters}
          >
            Clear all filters
          </Button>
        </div>
      )}
    </div>
  );
}
