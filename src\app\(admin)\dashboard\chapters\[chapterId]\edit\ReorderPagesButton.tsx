'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { reorderPages } from '@/actions/chapterActions';
import { toast } from 'sonner';
import { MoveVertical } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import { Page } from '@/generated/prisma';

interface ReorderPagesButtonProps {
  chapterId: string;
  pages: Page[];
}

export function ReorderPagesButton({ chapterId, pages }: ReorderPagesButtonProps) {
  const [isReordering, setIsReordering] = useState(false);
  const [reorderedPages, setReorderedPages] = useState<Page[]>([...pages].sort((a, b) => a.pageNumber - b.pageNumber));

  const movePageUp = (index: number) => {
    if (index <= 0) return;
    const newPages = [...reorderedPages];
    [newPages[index], newPages[index - 1]] = [newPages[index - 1], newPages[index]];
    setReorderedPages(newPages);
  };

  const movePageDown = (index: number) => {
    if (index >= reorderedPages.length - 1) return;
    const newPages = [...reorderedPages];
    [newPages[index], newPages[index + 1]] = [newPages[index + 1], newPages[index]];
    setReorderedPages(newPages);
  };

  const handleSaveOrder = async () => {
    setIsReordering(true);

    const pageOrder = reorderedPages.map((page, index) => ({
      id: page.id,
      newPageNumber: index + 1,
    }));

    const promise = () => new Promise(async (resolve, reject) => {
      const formData = new FormData();
      formData.append('chapterId', chapterId);
      formData.append('pageOrder', JSON.stringify(pageOrder));

      const result = await reorderPages({
        success: false,
        message: '',
      }, formData);

      if (result.success) {
        resolve(result.message);
      } else {
        reject(new Error(result.message));
      }
    });

    toast.promise(promise(), {
      loading: 'Saving page order...',
      success: (message) => {
        // Force a refresh to show the updated page order
        window.location.reload();
        return `${message}`;
      },
      error: (error) => `${error.message || 'Failed to reorder pages.'}`,
      finally: () => setIsReordering(false),
    });
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" className="ml-2">
          <MoveVertical className="h-4 w-4 mr-2" />
          Reorder Pages
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Reorder Pages</DialogTitle>
          <DialogDescription>
            Drag pages to reorder them or use the up/down buttons.
          </DialogDescription>
        </DialogHeader>

        <div className="max-h-[60vh] overflow-y-auto">
          <div className="space-y-2">
            {reorderedPages.map((page, index) => (
              <div key={page.id} className="flex items-center gap-2 p-2 border rounded">
                <div className="flex-shrink-0 w-8 text-center font-medium">
                  {index + 1}
                </div>
                <div className="flex-grow flex items-center gap-2">
                  <div className="h-12 w-12 bg-muted relative flex-shrink-0">
                    {page.imagePath && (
                      <img
                        src={`/uploads/${page.imagePath}`}
                        alt={`Page ${page.pageNumber}`}
                        className="h-full w-full object-contain"
                      />
                    )}
                  </div>
                  <span className="text-sm">Page {page.pageNumber}</span>
                </div>
                <div className="flex-shrink-0 flex flex-col gap-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => movePageUp(index)}
                    disabled={index === 0}
                  >
                    ↑
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => movePageDown(index)}
                    disabled={index === reorderedPages.length - 1}
                  >
                    ↓
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>

        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline" disabled={isReordering}>Cancel</Button>
          </DialogClose>
          <Button
            onClick={handleSaveOrder}
            disabled={isReordering}
          >
            {isReordering ? 'Saving...' : 'Save Order'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
