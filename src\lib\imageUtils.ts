/**
 * Utility functions for handling images in the application
 */

/**
 * Get the appropriate image URL based on the path and type
 * @param path The image path
 * @param type The type of image (cover or page)
 * @returns The complete image URL
 */
export function getImageUrl(path: string | null, type: 'cover' | 'page' = 'page'): string {
  if (!path) return '';

  // Handle absolute URLs (e.g., from CDN)
  if (path.startsWith('http')) return path;

  // If path already includes /api/image, don't modify it
  if (path.startsWith('/api/image')) return path;

  // Normalize the path for the API route
  let normalizedPath = path;

  // Remove leading slashes if present
  if (path.startsWith('/uploads/')) {
    normalizedPath = path.substring('/uploads/'.length);
  } else if (path.startsWith('/covers/')) {
    normalizedPath = 'covers/' + path.substring('/covers/'.length);
  } else if (path.startsWith('/')) {
    normalizedPath = path.substring(1);
  } else if (!path.startsWith('covers/') && type === 'cover') {
    // Add covers/ prefix for cover images if not already present
    normalizedPath = 'covers/' + path;
  }

  // Use the API route with query parameter
  return `/api/image?path=${encodeURIComponent(normalizedPath)}`;
}

/**
 * Generate a simple blur data URL for image placeholders
 * @returns A tiny transparent placeholder image as a data URL
 */
export function getDefaultBlurDataURL(): string {
  // Simple transparent placeholder
  return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEtAJJXIDTjwAAAABJRU5ErkJggg==';
}

/**
 * Get appropriate sizes attribute for responsive images
 * @param type The type of image layout
 * @returns The sizes attribute string
 */
export function getImageSizes(type: 'card' | 'cover' | 'fullWidth' | 'thumbnail'): string {
  switch (type) {
    case 'card':
      return '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw';
    case 'cover':
      return '(max-width: 768px) 100vw, 300px';
    case 'thumbnail':
      return '(max-width: 768px) 80px, 40px';
    case 'fullWidth':
    default:
      return '100vw';
  }
}
