import { getSiteStatistics } from '@/actions/statsActions';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export default async function StatsPage() {
  const stats = await getSiteStatistics();

  if (!stats) {
    return (
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">Statistics</h1>
        <p>You need to be logged in to view this page.</p>
      </div>
    );
  }

  // Format date for display
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Format month for display
  const formatMonth = (monthKey: string) => {
    const [year, month] = monthKey.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1);
    return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
  };

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-white">Site Statistics</h1>
      </div>

      <div className="bg-[#343450] rounded-lg p-6 mb-8">
        <p className="text-gray-300">
          This page shows statistics and analytics for your manga reader site.
        </p>
      </div>

      {/* Overview Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-[#343450] rounded-lg p-6 shadow-md">
          <h2 className="text-lg font-semibold mb-2 text-white">Total Series</h2>
          <p className="text-3xl font-bold text-primary">{stats.totalSeries}</p>
        </div>
        
        <div className="bg-[#343450] rounded-lg p-6 shadow-md">
          <h2 className="text-lg font-semibold mb-2 text-white">Total Chapters</h2>
          <p className="text-3xl font-bold text-[#6e6bff]">{stats.totalChapters}</p>
        </div>
        
        <div className="bg-[#343450] rounded-lg p-6 shadow-md">
          <h2 className="text-lg font-semibold mb-2 text-white">Total Pages</h2>
          <p className="text-3xl font-bold text-[#8a7bff]">{stats.totalPages}</p>
        </div>
      </div>

      {/* Latest Activity */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Latest Series */}
        <div className="bg-[#2a2a3c] rounded-lg p-6 shadow-md">
          <h2 className="text-lg font-semibold mb-4 text-white">Latest Series</h2>
          {stats.latestSeries ? (
            <div className="space-y-2">
              <p className="text-gray-300">
                <span className="font-medium text-white">Title:</span>{' '}
                <Link href={`/dashboard/series/${stats.latestSeries.id}/edit`} className="text-primary hover:underline">
                  {stats.latestSeries.title}
                </Link>
              </p>
              <p className="text-gray-300">
                <span className="font-medium text-white">Added:</span>{' '}
                {formatDate(stats.latestSeries.createdAt)}
              </p>
              <div className="mt-4">
                <Button variant="outline" size="sm" asChild>
                  <Link href={`/dashboard/series/${stats.latestSeries.id}/edit`}>View Series</Link>
                </Button>
              </div>
            </div>
          ) : (
            <p className="text-gray-400">No series have been added yet.</p>
          )}
        </div>

        {/* Latest Chapter */}
        <div className="bg-[#2a2a3c] rounded-lg p-6 shadow-md">
          <h2 className="text-lg font-semibold mb-4 text-white">Latest Chapter</h2>
          {stats.latestChapter ? (
            <div className="space-y-2">
              <p className="text-gray-300">
                <span className="font-medium text-white">Series:</span>{' '}
                <Link href={`/dashboard/series/${stats.latestChapter.mangaSeriesId}/edit`} className="text-primary hover:underline">
                  {stats.latestChapter.mangaSeriesTitle}
                </Link>
              </p>
              <p className="text-gray-300">
                <span className="font-medium text-white">Chapter:</span>{' '}
                {stats.latestChapter.chapterNumber}
              </p>
              <p className="text-gray-300">
                <span className="font-medium text-white">Uploaded:</span>{' '}
                {formatDate(stats.latestChapter.uploadDate)}
              </p>
              <div className="mt-4">
                <Button variant="outline" size="sm" asChild>
                  <Link href={`/dashboard/chapters/${stats.latestChapter.id}/edit`}>View Chapter</Link>
                </Button>
              </div>
            </div>
          ) : (
            <p className="text-gray-400">No chapters have been uploaded yet.</p>
          )}
        </div>
      </div>

      {/* Series with Most Chapters */}
      <div className="bg-[#2a2a3c] rounded-lg p-6 shadow-md">
        <h2 className="text-lg font-semibold mb-4 text-white">Series with Most Chapters</h2>
        {stats.seriesWithMostChapters ? (
          <div className="space-y-2">
            <p className="text-gray-300">
              <span className="font-medium text-white">Title:</span>{' '}
              <Link href={`/dashboard/series/${stats.seriesWithMostChapters.id}/edit`} className="text-primary hover:underline">
                {stats.seriesWithMostChapters.title}
              </Link>
            </p>
            <p className="text-gray-300">
              <span className="font-medium text-white">Chapter Count:</span>{' '}
              <span className="text-[#6e6bff] font-semibold">{stats.seriesWithMostChapters.chapterCount}</span>
            </p>
            <div className="mt-4">
              <Button variant="outline" size="sm" asChild>
                <Link href={`/dashboard/series/${stats.seriesWithMostChapters.id}/edit`}>View Series</Link>
              </Button>
            </div>
          </div>
        ) : (
          <p className="text-gray-400">No series with chapters found.</p>
        )}
      </div>

      {/* Series by Status */}
      <div className="bg-[#2a2a3c] rounded-lg p-6 shadow-md">
        <h2 className="text-lg font-semibold mb-4 text-white">Series by Status</h2>
        {stats.seriesByStatus.length > 0 ? (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Count</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {stats.seriesByStatus.map((item) => (
                  <TableRow key={item.status}>
                    <TableCell className="font-medium">{item.status}</TableCell>
                    <TableCell className="text-right">{item.count}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <p className="text-gray-400">No series status data available.</p>
        )}
      </div>

      {/* Chapters by Month */}
      <div className="bg-[#2a2a3c] rounded-lg p-6 shadow-md">
        <h2 className="text-lg font-semibold mb-4 text-white">Chapters Added (Last 6 Months)</h2>
        {stats.chaptersByMonth.length > 0 ? (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Month</TableHead>
                  <TableHead className="text-right">Chapters Added</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {stats.chaptersByMonth.map((item) => (
                  <TableRow key={item.month}>
                    <TableCell className="font-medium">{formatMonth(item.month)}</TableCell>
                    <TableCell className="text-right">{item.count}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <p className="text-gray-400">No chapter upload data available.</p>
        )}
      </div>

      {/* Visual representation of monthly uploads */}
      <div className="bg-[#2a2a3c] rounded-lg p-6 shadow-md">
        <h2 className="text-lg font-semibold mb-4 text-white">Monthly Chapter Uploads</h2>
        <div className="h-64 flex items-end space-x-2">
          {stats.chaptersByMonth.map((item) => {
            const maxCount = Math.max(...stats.chaptersByMonth.map(i => i.count));
            const height = maxCount > 0 ? (item.count / maxCount) * 100 : 0;
            
            return (
              <div key={item.month} className="flex flex-col items-center flex-1">
                <div 
                  className="w-full bg-[#6e6bff] rounded-t"
                  style={{ height: `${height}%` }}
                ></div>
                <div className="text-xs text-gray-400 mt-2 transform -rotate-45 origin-top-left">
                  {formatMonth(item.month)}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
