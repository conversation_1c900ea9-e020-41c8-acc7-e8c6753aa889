// src/app/privacy/page.tsx
import React from 'react';

export default function PrivacyPage() {
	return (
		<div className="container mx-auto px-4 py-8">
			<div className="max-w-3xl mx-auto bg-[#343450] rounded-lg p-8 shadow-lg">
				<h1 className="mb-6 text-3xl font-bold text-white">Privacy Policy</h1>
				<div className="space-y-4 text-gray-200">
					<p>
						This is a placeholder privacy policy page for the Taroo MVP.
					</p>
					<p>
						In a real application, this page would contain detailed information about how user data is collected, 
						stored, and processed, as well as information about cookies and third-party services.
					</p>
					
					<h2 className="text-xl font-semibold mt-6 mb-3 text-white">Information We Collect</h2>
					<p>
						Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, 
						nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.
					</p>
					
					<h2 className="text-xl font-semibold mt-6 mb-3 text-white">How We Use Your Information</h2>
					<p>
						Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, 
						nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.
					</p>
					
					<h2 className="text-xl font-semibold mt-6 mb-3 text-white">Cookies</h2>
					<p>
						Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, 
						nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.
					</p>
					
					<h2 className="text-xl font-semibold mt-6 mb-3 text-white">Third-Party Services</h2>
					<p>
						Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, 
						nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.
					</p>
					
					<h2 className="text-xl font-semibold mt-6 mb-3 text-white">Contact Us</h2>
					<p>
						If you have any questions about our privacy policy, please contact <NAME_EMAIL>.
					</p>
				</div>
			</div>
		</div>
	);
}
