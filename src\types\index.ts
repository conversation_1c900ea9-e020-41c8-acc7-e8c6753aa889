/**
 * Type definitions for the Taroo manga reader application
 */

// Use the main Prisma client type import
import type { AdminUser, MangaSeries, Chapter, Page } from '@/generated/prisma';

// Re-export Prisma types for convenience
export type { AdminUser, MangaSeries, Chapter, Page };

// Extended types with relationships
export interface MangaSeriesWithChapters extends MangaSeries {
  chapters: Chapter[];
}

export interface ChapterWithPages extends Chapter {
  pages: Page[];
  mangaSeries?: MangaSeries;
}

export interface ChapterWithMangaSeries extends Chapter {
  mangaSeries: Pick<MangaSeries, 'id' | 'title' | 'coverImage'>;
  pageCount?: number;
}

// Auth related types
export interface SessionUser {
  id: string;
  username: string;
}
