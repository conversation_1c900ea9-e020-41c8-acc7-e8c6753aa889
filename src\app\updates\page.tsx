// src/app/updates/page.tsx
import { prisma } from '@/lib/prisma';
import Link from 'next/link';
import type { Chapter } from '@/generated/prisma';

// Define a type for the chapter data including the related series title
interface ChapterWithSeriesTitle extends Chapter {
  mangaSeries: {
    id: string;
    title: string;
  };
}

export default async function UpdatesPage() {
  // Fetch latest chapters
  const latestChapters: ChapterWithSeriesTitle[] = await prisma.chapter.findMany({
    take: 30, // Show more chapters on the dedicated updates page
    orderBy: {
      uploadDate: 'desc',
    },
    include: {
      mangaSeries: {
        select: {
          id: true,
          title: true,
        },
      },
    },
  });

  return (
    <div className="space-y-8">
      <div className="bg-[#343450] rounded-lg p-8 mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Latest Updates</h1>
        <p className="text-gray-300">Stay up to date with the newest chapter releases</p>
      </div>

      {latestChapters.length > 0 ? (
        <div className="bg-[#2a2a3c] rounded-lg overflow-hidden">
          <ul className="divide-y divide-[#3d3d59]">
            {latestChapters.map((chapter: ChapterWithSeriesTitle) => (
              <li key={chapter.id} className="hover:bg-[#3d3d59] transition-colors">
                <Link 
                  href={`/read/${chapter.mangaSeries.id}/${chapter.chapterNumber}`}
                  className="block p-4"
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-medium text-primary hover:underline">
                        {chapter.mangaSeries.title}
                      </span>
                      <span className="text-gray-300"> - Chapter {chapter.chapterNumber}</span>
                      {chapter.title && (
                        <span className="text-gray-400 italic">: {chapter.title}</span>
                      )}
                    </div>
                    <span className="text-sm text-gray-400">
                      {new Date(chapter.uploadDate).toLocaleDateString()}
                    </span>
                  </div>
                </Link>
              </li>
            ))}
          </ul>
        </div>
      ) : (
        <p className="text-gray-300">No recent chapters found.</p>
      )}
    </div>
  );
}
