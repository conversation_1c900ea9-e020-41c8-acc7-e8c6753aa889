'use client';

import Link from 'next/link';
import { getSeriesList } from '@/actions/seriesActions';
import { Button } from '@/components/ui/button';
// Ensure Table components are imported correctly from the generated file
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"; // Corrected path
import { SeriesTableActions } from './SeriesTableActions';
import { MangaSeries } from '@/generated/prisma';
import { useEffect, useState } from 'react';
import { OptimizedImage } from '@/components/ui/optimized-image';

export default function SeriesListPage() {
  const [seriesList, setSeriesList] = useState<MangaSeries[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSeries = async () => {
      try {
        setLoading(true);
        const series = await getSeriesList();
        setSeriesList(series);
        setError(null);
      } catch (err) {
        console.error("Failed to fetch series:", err);
        setError('Failed to load series list.');
      } finally {
        setLoading(false);
      }
    };
    fetchSeries();
  }, []); // Refetch triggered by revalidatePath in actions

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Manga Series</h1>
        <Link href="/dashboard/series/new">
          <Button>Create New Series</Button>
        </Link>
      </div>

      {loading && <p>Loading...</p>}
      {error && <p className="text-red-500">{error}</p>}

      {!loading && !error && (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[80px]">Cover</TableHead>
              <TableHead>Title</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Author</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {seriesList.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center">
                  No series found.
                </TableCell>
              </TableRow>
            ) : (
              seriesList.map((series) => (
                <TableRow key={series.id}>
                  <TableCell>
                    <div className="relative w-[50px] h-[70px] overflow-hidden rounded">
                      <OptimizedImage
                        src={series.coverImage}
                        alt={series.title}
                        fill
                        sizesType="thumbnail"
                        className="rounded"
                        fallbackClassName="w-full h-full bg-gray-200"
                        fallbackText="No Cover"
                        usePlaceholder={false}
                      />
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">{series.title}</TableCell>
                  <TableCell>{series.status || 'N/A'}</TableCell>
                  <TableCell>{series.author || 'N/A'}</TableCell>
                  <TableCell className="text-right">
                    <SeriesTableActions series={series} />
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      )}
    </div>
  );
}
