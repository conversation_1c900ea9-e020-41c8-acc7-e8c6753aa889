'use client';

import { useState, useEffect } from 'react';
import { DatabaseError } from '@/components/ui/database-error';

interface HomePageWrapperProps {
  children: React.ReactNode;
}

export function HomePageWrapper({ children }: HomePageWrapperProps) {
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    // Listen for unhandled errors that might be database-related
    const handleError = (event: ErrorEvent) => {
      if (
        event.error?.message?.includes("database") ||
        event.error?.message?.includes("prisma") ||
        event.error?.message?.includes("Can't reach database server")
      ) {
        console.error('Database connection error detected:', event.error);
        setError(event.error);
        // Prevent the default error handling
        event.preventDefault();
      }
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (error) {
    return (
      <DatabaseError 
        message="Cannot connect to the database. Please make sure your database server is running at localhost:5432." 
      />
    );
  }

  return <>{children}</>;
}
