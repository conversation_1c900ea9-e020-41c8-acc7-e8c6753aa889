/**
 * NOTE: This file uses server-only functions for password hashing
 * These functions should only be used in server components or server actions
 */

// Mark this file as server-only to prevent it from being used in client components
'use server';

import bcrypt from 'bcrypt';

const SALT_ROUNDS = 10; // Standard salt rounds for bcrypt

/**
 * Hashes a password using bcrypt.
 * @param password The plain text password to hash.
 * @returns The hashed password.
 */
export async function hashPassword(password: string): Promise<string> {
  if (!password) {
    throw new Error("Password cannot be empty.");
  }
  const hashedPassword = await bcrypt.hash(password, SALT_ROUNDS);
  return hashedPassword;
}

/**
 * Verifies a plain text password against a stored bcrypt hash.
 * @param password The plain text password to verify.
 * @param storedHash The stored bcrypt password hash.
 * @returns True if the password matches the hash, false otherwise.
 */
export async function verifyPassword(password: string, storedHash: string): Promise<boolean> {
  if (!password || !storedHash) {
    // Avoid bcrypt errors with empty inputs
    return false;
  }
  const isMatch = await bcrypt.compare(password, storedHash);
  return isMatch;
}
