// src/app/about/page.tsx
import React from 'react';

export default function AboutPage() {
	return (
		<div className="container mx-auto px-4 py-8">
			<div className="max-w-3xl mx-auto bg-[#343450] rounded-lg p-8 shadow-lg">
				<h1 className="mb-6 text-3xl font-bold text-white">About Taroo</h1>
				<div className="space-y-4 text-gray-200">
					<p>
						Taroo is a simple manga reader application built as an MVP project.
					</p>
					<p>
						This project demonstrates the use of Next.js (App Router), Prisma,
						PostgreSQL, NextAuth.js, Tailwind CSS, and Shadcn/ui.
					</p>
					<p>
						Features include admin management for manga series and chapters, image
						uploads, and a frontend reader with different view modes.
					</p>
				</div>
			</div>
		</div>
	);
}
