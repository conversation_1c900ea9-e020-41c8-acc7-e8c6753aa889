/**
 * Custom Prisma type definitions to ensure compatibility with Vercel deployments
 */

// Define common input types that might be missing from the generated Prisma client
export interface PageCreateManyInput {
  id?: string;
  pageNumber: number;
  imagePath: string;
  chapterId: string;
  createdAt?: Date | string;
}

export interface ChapterCreateManyInput {
  id?: string;
  chapterNumber: string;
  title?: string | null;
  uploadDate?: Date | string;
  mangaSeriesId: string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
}

export interface MangaSeriesCreateManyInput {
  id?: string;
  title: string;
  description?: string | null;
  coverImage?: string | null;
  status?: string | null;
  author?: string | null;
  artist?: string | null;
  genres?: string | null;
  createdAt?: Date | string;
  updatedAt?: Date | string;
}

// Add more types as needed
