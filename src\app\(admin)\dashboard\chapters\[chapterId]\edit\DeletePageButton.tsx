'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { deletePage } from '@/actions/chapterActions';
import { toast } from 'sonner';
import { Trash2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';

interface DeletePageButtonProps {
  pageId: string;
  pageNumber: number;
}

export function DeletePageButton({ pageId, pageNumber }: DeletePageButtonProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    setIsDeleting(true);

    const promise = () => new Promise(async (resolve, reject) => {
      const formData = new FormData();
      formData.append('pageId', pageId);

      const result = await deletePage({
        success: false,
        message: '',
      }, formData);

      if (result.success) {
        resolve(result.message);
      } else {
        reject(new Error(result.message));
      }
    });

    toast.promise(promise(), {
      loading: 'Deleting page...',
      success: (message) => {
        // Force a refresh to show the updated page list
        window.location.reload();
        return `${message}`;
      },
      error: (error) => `${error.message || 'Failed to delete page.'}`,
      finally: () => setIsDeleting(false),
    });
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="h-6 w-6">
          <Trash2 className="h-4 w-4 text-destructive" />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Page</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete Page {pageNumber}? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline" disabled={isDeleting}>Cancel</Button>
          </DialogClose>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting ? 'Deleting...' : 'Delete Page'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
